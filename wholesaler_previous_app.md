# Wholesaler Item Creation & Edit Workflow

This document describes the full workflow for wholesalers to create or edit items in the system, including all user-facing pages and selection steps.

---

## Overview
Wholesalers can add new items or edit existing ones through a multi-step form. The process involves selecting a company, product, region(s) for pricing, pricing type, and measurement unit. Each step is handled by a dedicated page or dialog, ensuring a guided and error-resistant experience.

---

## 1. Entry Point: Item Form Page
- **File:** `lib/features_old/wholesaler/item_form/item_form_page.dart`
- **Controller:** `ItemFormController`
- **Modes:**
  - **Create:** No item provided, form is empty.
  - **Edit:** Existing item provided, form is pre-filled.

### Main Fields & Steps
- **Company Selection** (optional, but recommended)
- **Product Selection** (required)
- **Base Price** (required)
- **Inventory Count** (required)
- **Price Expiry Date** (required)
- **Region Pricing** (optional, for region-specific prices)
- **Pricing Type** (optional, if enabled)
- **Measurement Unit** (optional, if enabled)

---

## 2. Company Selection
- **Page:** `CompanySelectionPage`
- **Accessed via:** Tapping the company field in the item form (if not editing)
- **Purpose:** Lets the user pick a company. Selecting a company resets the product selection.

---

## 3. Product Selection
- **Page:** `ProductSelectionPage`
- **Accessed via:** Tapping the product field in the item form (if not editing)
- **Purpose:** Lets the user pick a product, optionally filtered by the selected company.
- **Behavior:**
  - If a company is selected, only products from that company are shown.
  - Selecting a product with an associated company auto-selects that company if not already set.

---


## 5. Pricing Type Selection
- **Page:** `PricingTypeSelectionPage` (`lib/features_old/wholesaler/pricing_type_selection/pricing_type_selection_page.dart`)
- **Purpose:** Lets the user select a pricing type (e.g., retail, wholesale, etc.).
- **Features:**
  - Search bar to filter pricing types.
  - List of available types, selectable.
  - Selected type is highlighted.

---

## 6. Measurement Unit Selection
- **Page:** `MeasurementUnitSelectionPage` (`lib/features_old/wholesaler/measurement_unit_selection/measurement_unit_selection_page.dart`)
- **Purpose:** Lets the user select a measurement unit for the item (e.g., kg, piece, box).
- **Features:**
  - Search bar to filter units.
  - List of available units, selectable.
  - Selected unit is highlighted.

---

## 7. Form Submission
- **Validation:**
  - All required fields must be filled.
  - Price and inventory must be valid numbers.
  - Region prices must be positive numbers.
- **Submission:**
  - On submit, the controller creates or updates the item via the repository.
  - Errors are shown inline at the top of the form.
  - On success, the user is navigated back to the previous screen.

---

## 8. Error Handling
- **Inline error messages** for validation and submission errors.
- **Loading indicators** during submission and selection searches.

---

## 9. User Experience Notes
- **Editing disables company and product selection** to prevent accidental changes to core item identity.
- **All selection pages use search and filtering** for large datasets.
- **Region pricing is optional** but recommended for market-specific pricing.

---

## 10. Related Files
- `item_form_page.dart`, `item_form_controller.dart`
- `region_selection_page.dart`, `region_selection_controller.dart`
- `pricing_type_selection_page.dart`, `pricing_type_selection_controller.dart`
- `measurement_unit_selection_page.dart`, `measurement_unit_selection_controller.dart`
- `company_selection_page.dart`, `product_selection_page.dart`

---

## 11. Example Workflow (Create New Item)
1. Open the item form page.
2. (Optional) Select a company.
3. Select a product (required).
4. Enter base price and inventory count.
5. (Optional) Set price expiry date.
6. (Optional) Add region-specific prices.
7. (Optional) Select pricing type.
8. (Optional) Select measurement unit.
9. Submit the form.
10. On success, return to the item list.

---

## 12. Example Workflow (Edit Item)
1. Open the item form page with an existing item.
2. Company and product fields are disabled.
3. Update price, inventory, expiry, region prices, etc.
4. Submit the form.
5. On success, return to the item list.

---

## 13. Notes
- The workflow is designed for flexibility and scalability.
- All selection pages are modular and reusable.
- The form supports both English and Arabic (RTL) layouts. 