{"openapi": "3.1.0", "info": {"title": "NinjaAPI", "version": "1.0.0", "description": ""}, "paths": {"/api/docs/tag/{tag}": {"get": {"operationId": "core_api_docs_get_docs_by_tag", "summary": "Get API documentation for a specific tag", "parameters": [{"in": "path", "name": "tag", "schema": {"title": "Tag", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Get OpenAPI documentation for all endpoints with a specific tag,\nincluding the request/response schemas they use.", "tags": ["docs"]}}, "/api/": {"get": {"operationId": "core_urls_test", "summary": "Test", "parameters": [], "responses": {"200": {"description": "OK"}}}}, "/api/companies": {"post": {"operationId": "products_views_create_company", "summary": "Create Company", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOut"}}}}}, "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyIn"}}}, "required": true}}, "get": {"operationId": "products_views_list_companies", "summary": "List Companies", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CompanyOut"}, "title": "Response", "type": "array"}}}}}, "tags": ["products"]}}, "/api/categories": {"post": {"operationId": "products_views_create_category", "summary": "Create Category", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryOut"}}}}}, "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryIn"}}}, "required": true}}, "get": {"operationId": "products_views_list_categories", "summary": "List Categories", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CategoryOut"}, "title": "Response", "type": "array"}}}}}, "tags": ["products"]}}, "/api/categories/{category_id}": {"get": {"operationId": "products_views_get_category", "summary": "Get Category", "parameters": [{"in": "path", "name": "category_id", "schema": {"title": "Category Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryOut"}}}}}, "tags": ["products"]}}, "/api/products": {"post": {"operationId": "products_views_create_product", "summary": "Create Product", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductOut"}}}}}, "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductIn"}}}, "required": true}}}, "/api/products/{product_id}": {"put": {"operationId": "products_views_update_product", "summary": "Update Product", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductOut"}}}}}, "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUpdate"}}}, "required": true}}}, "/api/products/": {"get": {"operationId": "products_views_list_search_products", "summary": "List Search Products", "parameters": [{"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "size", "schema": {"default": 10, "title": "Size", "type": "integer"}, "required": false}, {"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedResponse_ProductOut_"}}}}}, "tags": ["products"]}}, "/api/regions": {"get": {"operationId": "products_views_list_regions", "summary": "List Regions", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all regions with hierarchical names", "tags": ["products"]}}, "/api/regions/{region_id}": {"get": {"operationId": "products_views_get_region", "summary": "Get Region", "parameters": [{"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}}}}}, "description": "Get a specific region with hierarchical name", "tags": ["products"]}}, "/api/exists": {"post": {"operationId": "products_views_check_existence", "summary": "Check Existence", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExistenceResponse"}}}}}, "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchInput"}}}, "required": true}}}, "/api/home/<USER>": {"get": {"operationId": "products_home_views_get_categories", "summary": "Get Categories", "parameters": [{"in": "query", "name": "limit", "schema": {"default": 10, "title": "Limit", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CategoryOut"}, "title": "Response", "type": "array"}}}}}, "description": "Get a list of categories.\nResults are cached for 24 hours.", "tags": ["home"]}}, "/api/accounts/signup": {"post": {"operationId": "accounts_views_auth_views_signup", "summary": "Signup", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignupResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "description": "Register a new user and initiate OTP verification\n\nThis endpoint:\n1. Creates a new user account\n2. Requests and sends an OTP for phone verification\n3. Returns user details for verification", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignupRequest"}}}, "required": true}}}, "/api/accounts/signup/trusted": {"post": {"operationId": "accounts_views_auth_views_signup_trusted", "summary": "Signup Trusted", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrustedSignupResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "description": "Register a new user without phone verification (for trusted users only)\n\nThis endpoint:\n1. Creates a new user account\n2. Marks the phone as verified\n3. Returns JWT token and user details", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignupRequest"}}}, "required": true}}}, "/api/accounts/verify/verify-phone": {"post": {"operationId": "accounts_views_auth_views_verify_phone", "summary": "Verify Phone", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "description": "Verify phone number with OTP\n\nThis endpoint:\n1. Verifies the OTP\n2. Marks the phone as verified\n3. Returns JWT token and user details", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}}}, "/api/accounts/login": {"post": {"operationId": "accounts_views_auth_views_login", "summary": "<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "description": "Login with phone number and password\n\nThis endpoint:\n1. Authenticates the user with phone and password\n2. Checks if phone is verified\n3. Returns JWT token and user details", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}}}, "/api/accounts/me": {"get": {"operationId": "accounts_views_auth_views_get_current_user", "summary": "Get Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "description": "Get the current authenticated user's data\n\nThis endpoint:\n1. Uses the JWT token to identify the user\n2. Returns the user's profile data", "tags": ["auth"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "accounts_views_auth_views_update_current_user", "summary": "Update Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "description": "Update the current authenticated user's data\n\nThis endpoint:\n1. Uses the JWT token to identify the user\n2. Updates the user's profile with the provided data\n3. Returns the updated user profile\n\nFields that can be updated:\n- first_name\n- last_name\n- email\n- phone (requires verification)", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/accounts/fcm-token": {"post": {"operationId": "accounts_views_main_views_update_fcm_token", "summary": "Update Fcm Token", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFCMTokenRequest"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers": {"post": {"operationId": "wholesalers_views_create_wholesaler", "summary": "Create Wholesaler", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Create a new wholesaler store. Required when user first signs in as a wholesaler.", "tags": ["wholesalers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me": {"get": {"operationId": "wholesalers_views_get_my_wholesaler", "summary": "Get My Wholesaler", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Get the current user's wholesaler store", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "wholesalers_views_update_my_wholesaler", "summary": "Update My Wholesaler", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Update the current user's wholesaler store", "tags": ["wholesalers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/logo": {"post": {"operationId": "wholesalers_views_upload_logo", "summary": "Upload Logo", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Upload a logo for the wholesaler", "tags": ["wholesalers"], "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"file": {"format": "binary", "title": "File", "type": "string"}}, "required": ["file"], "title": "FileParams", "type": "object"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/background": {"post": {"operationId": "wholesalers_views_upload_background", "summary": "Upload Background", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Upload a background image for the wholesaler", "tags": ["wholesalers"], "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"file": {"format": "binary", "title": "File", "type": "string"}}, "required": ["file"], "title": "FileParams", "type": "object"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/min-charges": {"post": {"operationId": "wholesalers_views_create_min_charge", "summary": "Create Min Charge", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionMinChargeOut"}}}}}, "description": "Set minimum charge for a specific region", "tags": ["wholesalers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionMinChargeIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "get": {"operationId": "wholesalers_views_list_min_charges", "summary": "List Min Charges", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionMinChargeOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all minimum charges for the current wholesaler", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/items": {"post": {"operationId": "wholesalers_views_create_item", "summary": "Create <PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemOut"}}}}}, "description": "Add a product with base pricing to the wholesaler's inventory", "tags": ["wholesalers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "get": {"operationId": "wholesalers_views_list_items", "summary": "List Items", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ItemOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all products in the wholesaler's inventory", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/items/{item_id}": {"get": {"operationId": "wholesalers_views_get_item", "summary": "Get Item", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemOut"}}}}}, "description": "Get a specific product from the wholesaler's inventory", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "wholesalers_views_update_item", "summary": "Update Item", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemOut"}}}}}, "description": "Update a product's base pricing and inventory in the wholesaler's inventory", "tags": ["wholesalers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItemUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "wholesalers_views_delete_item", "summary": "Delete Item", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Remove a product from the wholesaler's inventory (soft delete)", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/items/{item_id}/expire": {"put": {"operationId": "wholesalers_views_expire_item", "summary": "Expire Item", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Immediately expire a product's pricing", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/wholesalers/me/items/{item_id}/inventory": {"post": {"operationId": "wholesalers_views_update_inventory", "summary": "Update Inventory", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryUpdateResponse"}}}}}, "description": "Add or remove inventory from an item", "tags": ["wholesalers"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InventoryTransactionIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "get": {"operationId": "wholesalers_views_list_inventory_transactions", "summary": "List Inventory Transactions", "parameters": [{"in": "path", "name": "item_id", "schema": {"title": "Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/InventoryTransactionOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all inventory transactions for an item", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/": {"get": {"operationId": "api_urls_test", "summary": "Test", "parameters": [], "responses": {"200": {"description": "OK"}}, "tags": ["v2"]}}, "/api/v2/login": {"post": {"operationId": "api_auth_login", "summary": "<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}, "description": "Simple login with phone and password\nReturns JWT token and user details including wholesaler_id if applicable", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}}}, "/api/v2/register": {"post": {"operationId": "api_auth_register", "summary": "Register", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}}}, "description": "Simple user registration\nCreates user account with phone verification set to False", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}}}, "/api/v2/me": {"get": {"operationId": "api_auth_get_current_user", "summary": "Get Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "description": "Get the current authenticated user's data\nRequires authentication via JWT token", "tags": ["auth"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "api_auth_update_current_user", "summary": "Update Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "description": "Update the current authenticated user's data\nRequires authentication via JWT token\n\nFields that can be updated:\n- first_name\n- last_name\n- email\n- phone (requires verification if changed)", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/home/<USER>": {"get": {"operationId": "api_home_get_products", "summary": "Get Products", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}, {"in": "query", "name": "wholesaler_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductResponse"}}}}}, "description": "Get products with pagination and filtering support.\n\nQuery Parameters:\n- page: Page number (default: 1)\n- page_size: Number of items per page (default: 20, max: 100)\n- region_id: Filter by region ID (includes parent regions)\n- wholesaler_id: Filter by specific wholesaler ID\n- search: Search term for product name/title/description\n- category_id: Filter by category ID\n- company_id: Filter by company ID\n\nExample URLs:\n- /api/v2/home/<USER>\n- /api/v2/home/<USER>\n- /api/v2/home/<USER>\n\nNote: When wholesaler_id is provided, only that wholesaler's pricing will be returned.", "tags": ["home"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/products/{product_id}": {"get": {"operationId": "api_products_get_product_by_id", "summary": "Get Product By Id", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}, {"in": "query", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductResponse"}}}}}, "description": "Get a product by its ID and its prices from all wholesalers in the same region or parent regions.\n\nArgs:\n    product_id: The ID of the product to retrieve\n    region_id: The ID of the region to retrieve prices from (includes parent regions)\n\nReturns:\n    ProductResponse: The product with its prices from the region hierarchy", "tags": ["products"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/regions/": {"get": {"operationId": "api_regions_list_regions", "summary": "List Regions", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all regions with hierarchical names", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/regions/countries": {"get": {"operationId": "api_regions_get_countries", "summary": "Get Countries", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all countries with hierarchical names", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/regions/states": {"get": {"operationId": "api_regions_get_states", "summary": "Get States", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all states with hierarchical names", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/regions/districts": {"get": {"operationId": "api_regions_get_districts", "summary": "Get Districts", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all districts with hierarchical names", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/wholesalers/{wholesaler_id}": {"get": {"operationId": "api_wholesaler_get_wholesaler_by_id", "summary": "Get Wholesaler By Id", "parameters": [{"in": "path", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Get a wholesaler by its ID.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/wholesalers/{wholesaler_id}/min-charge/{region_id}": {"get": {"operationId": "api_wholesaler_get_wholesaler_min_charge", "summary": "Get Wholesaler Min Charge", "parameters": [{"in": "path", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}, {"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerMinChargeOut"}}}}}, "description": "Get the minimum charge for a specific wholesaler.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/wholesalers/{wholesaler_id}/items": {"get": {"operationId": "api_wholesaler_get_wholesaler_items", "summary": "Get Wholesaler Items", "parameters": [{"in": "path", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/WholesalerItemOut"}, "title": "Response", "type": "array"}}}}}, "description": "Get all items (products with pricing) for a specific wholesaler.\n\nArgs:\n    wholesaler_id: The ID of the wholesaler to get items for\n\nReturns:\n    List of WholesalerItemOut: Items with product details, pricing, and inventory info", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/wholesalers/items/{wholesaler_item_id}": {"get": {"operationId": "api_wholesaler_get_wholesaler_item_by_id", "summary": "Get Wholesaler Item By Id", "parameters": [{"in": "path", "name": "wholesaler_item_id", "schema": {"title": "Wholesaler Item Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerItemOut"}}}}}, "description": "Get a specific item from a wholesaler's inventory by item ID.\n\nArgs:\n    wholesaler_item_id: The ID of the wholesaler item to retrieve\n\nReturns:\n    WholesalerItemOut: Item with product details, pricing, and inventory info", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/stores/me": {"get": {"operationId": "api_stores_get_user_stores", "summary": "Get User Stores", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/StoreOut"}, "title": "Response", "type": "array"}}}}}, "description": "Get all stores for the authenticated user.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/stores/{store_id}": {"get": {"operationId": "api_stores_get_store_by_id", "summary": "Get Store By Id", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreOut"}}}}}, "description": "Get a single store by its ID for the authenticated user.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "api_stores_update_store", "summary": "Update Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreOut"}}}}}, "description": "Update an existing store for the authenticated user.", "tags": ["stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "api_stores_delete_store", "summary": "Delete Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Delete a store for the authenticated user.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/stores/": {"post": {"operationId": "api_stores_create_store", "summary": "Create Store", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreOut"}}}}}, "description": "Create a new store for the authenticated user.", "tags": ["stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/orders/": {"post": {"operationId": "api_orders_create_order", "summary": "Create Order", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/orders/my": {"get": {"operationId": "api_orders_get_my_orders", "summary": "Get My Orders", "parameters": [{"in": "query", "name": "offset", "schema": {"default": 0, "title": "Offset", "type": "integer"}, "required": false}, {"in": "query", "name": "limit", "schema": {"default": 10, "title": "Limit", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOrderResponse"}}}}}, "description": "Get all orders for the authenticated user.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/orders/stores/{store_id}/{order_id}": {"get": {"operationId": "api_orders_get_order_by_id", "summary": "Get Order By Id", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}, {"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Get a single order by its ID for a specific store owned by the authenticated user.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "api_orders_cancel_order", "summary": "Cancel Order", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}, {"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Cancel an order for a specific store owned by the authenticated user.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/orders/stores/{store_id}/{order_id}/status": {"post": {"operationId": "api_orders_update_order_status", "summary": "Update Order Status", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}, {"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Update the status of an order for a specific store owned by the authenticated user.", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusUpdateIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/v2/categories-companies/": {"get": {"operationId": "api_categories_companies_list_categories_companies", "summary": "List Categories Companies", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CategoryCompanyOut"}, "title": "Response", "type": "array"}}}}}, "tags": ["categories-companies"], "security": [{"AuthBearerMiddleware": []}]}}}, "components": {"schemas": {"CompanyOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["id", "name", "title", "slug"], "title": "CompanyOut", "type": "object"}, "CompanyIn": {"properties": {"name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["name", "title", "slug"], "title": "CompanyIn", "type": "object"}, "CategoryOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["id", "name", "title", "slug"], "title": "CategoryOut", "type": "object"}, "CategoryIn": {"properties": {"name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["name", "title", "slug"], "title": "CategoryIn", "type": "object"}, "ProductOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}}, "required": ["id", "name", "barcode"], "title": "ProductOut", "type": "object"}, "ProductIn": {"properties": {"name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"default": "", "title": "Description", "type": "string"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "image": {"title": "Image", "type": "string"}, "unit": {"default": "PIECE", "title": "Unit", "type": "string"}, "unit_count": {"anyOf": [{"type": "number"}, {"type": "string"}], "default": "1.0", "title": "Unit Count"}}, "required": ["name", "title", "barcode", "slug"], "title": "ProductIn", "type": "object"}, "ProductUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "barcode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Barcode"}, "slug": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Slug"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "unit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit"}, "unit_count": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Unit Count"}}, "title": "ProductUpdate", "type": "object"}, "PaginatedResponse_ProductOut_": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ProductOut"}, "title": "Items", "type": "array"}, "total": {"title": "Total", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "size": {"title": "Size", "type": "integer"}, "pages": {"title": "Pages", "type": "integer"}}, "required": ["items", "total", "page", "size", "pages"], "title": "PaginatedResponse[ProductOut]", "type": "object"}, "RegionWithHierarchyOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "code": {"title": "Code", "type": "string"}, "parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id"}, "hierarchical_name": {"title": "Hierarchical Name", "type": "string"}}, "required": ["id", "name", "type", "code", "hierarchical_name"], "title": "RegionWithHierarchyOut", "type": "object"}, "ExistenceResponse": {"properties": {"product_exists": {"title": "Product Exists", "type": "boolean"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}}, "required": ["product_exists"], "title": "ExistenceResponse", "type": "object"}, "SearchInput": {"properties": {"query": {"title": "Query", "type": "string"}, "search_in": {"enum": ["product", "company", "category"], "title": "Search In", "type": "string"}}, "required": ["query", "search_in"], "title": "SearchInput", "type": "object"}, "ProductPriceInfo": {"properties": {"price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Price"}, "wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "wholesaler": {"$ref": "#/components/schemas/WholesalerOut"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "price_expiry": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Price Expiry"}, "item_id": {"title": "Item Id", "type": "integer"}}, "required": ["price", "wholesaler_id", "wholesaler", "inventory_count", "item_id"], "title": "ProductPriceInfo", "type": "object"}, "ProductWithPricingOut": {"description": "Extended product information with pricing from wholesalers", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Unit Count"}, "lowest_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "The lowest available price from any wholesaler", "title": "Lowest Price"}, "lowest_price_wholesaler": {"anyOf": [{"$ref": "#/components/schemas/WholesalerOut"}, {"type": "null"}], "description": "The wholesaler offering the lowest price"}, "other_prices": {"description": "List of other available prices from different wholesalers", "items": {"$ref": "#/components/schemas/ProductPriceInfo"}, "title": "Other Prices", "type": "array"}, "price_range": {"anyOf": [{"additionalProperties": {"anyOf": [{"type": "number"}, {"type": "string"}]}, "type": "object"}, {"type": "null"}], "description": "The minimum and maximum available prices (min, max)", "title": "Price Range"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count"], "title": "ProductWithPricingOut", "type": "object"}, "WholesalerOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "username": {"title": "Username", "type": "string"}, "logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo Url"}, "background_image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Background Image Url"}}, "required": ["id", "title", "username"], "title": "WholesalerOut", "type": "object"}, "SignupResponse": {"properties": {"success": {"default": true, "title": "Success", "type": "boolean"}, "user_id": {"title": "User Id", "type": "integer"}, "message": {"title": "Message", "type": "string"}, "channel": {"title": "Channel", "type": "string"}}, "required": ["user_id", "message", "channel"], "title": "SignupResponse", "type": "object"}, "ErrorResponse": {"properties": {"error": {"title": "Error", "type": "string"}, "error_code": {"title": "Error Code", "type": "string"}}, "required": ["error", "error_code"], "title": "ErrorResponse", "type": "object"}, "SignupRequest": {"properties": {"name": {"description": "User's full name", "maxLength": 100, "minLength": 2, "title": "Name", "type": "string"}, "phone": {"description": "Phone number with country code (e.g., +201234567890)", "title": "Phone", "type": "string"}, "password": {"description": "User password (min 8 characters)", "minLength": 8, "title": "Password", "type": "string"}, "email": {"anyOf": [{"format": "email", "type": "string"}, {"type": "null"}], "description": "Optional email address", "title": "Email"}}, "required": ["name", "phone", "password"], "title": "SignupRequest", "type": "object"}, "TrustedSignupResponse": {"properties": {"success": {"default": true, "title": "Success", "type": "boolean"}, "token": {"title": "Token", "type": "string"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}}, "required": ["token", "user_id", "phone"], "title": "TrustedSignupResponse", "type": "object"}, "LoginResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "token": {"title": "Token", "type": "string"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}, "is_phone_verified": {"title": "Is Phone Verified", "type": "boolean"}, "wholesaler_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}}, "required": ["success", "token", "user_id", "phone", "is_phone_verified"], "title": "LoginResponse", "type": "object"}, "LoginRequest": {"properties": {"phone": {"title": "Phone", "type": "string"}, "password": {"title": "Password", "type": "string"}}, "required": ["phone", "password"], "title": "LoginRequest", "type": "object"}, "UserResponse": {"description": "Response schema for user data", "properties": {"id": {"title": "Id", "type": "integer"}, "username": {"title": "Username", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"title": "Phone", "type": "string"}, "phone_verified": {"title": "Phone Verified", "type": "boolean"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "is_active": {"title": "Is Active", "type": "boolean"}, "date_joined": {"format": "date-time", "title": "Date Joined", "type": "string"}, "wholesaler_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}}, "required": ["id", "username", "phone", "phone_verified", "is_active", "date_joined"], "title": "UserResponse", "type": "object"}, "UserUpdateRequest": {"description": "Request schema for updating user data", "properties": {"first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "title": "UserUpdateRequest", "type": "object"}, "SuccessResponse": {"properties": {"message": {"title": "Message", "type": "string"}}, "required": ["message"], "title": "SuccessResponse", "type": "object"}, "UpdateFCMTokenRequest": {"properties": {"fcm_token": {"title": "Fcm Token", "type": "string"}}, "required": ["fcm_token"], "title": "UpdateFCMTokenRequest", "type": "object"}, "WholesalerIn": {"properties": {"category": {"title": "Category", "type": "string"}, "title": {"title": "Title", "type": "string"}, "username": {"title": "Username", "type": "string"}}, "required": ["category", "title", "username"], "title": "WholesalerIn", "type": "object"}, "WholesalerUpdate": {"properties": {"category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}}, "title": "WholesalerUpdate", "type": "object"}, "RegionMinChargeOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "region": {"$ref": "#/components/schemas/RegionOut"}, "min_charge": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Min Charge"}, "min_items": {"title": "Min Items", "type": "integer"}}, "required": ["id", "region", "min_charge", "min_items"], "title": "RegionMinChargeOut", "type": "object"}, "RegionOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["id", "name", "type", "slug"], "title": "RegionOut", "type": "object"}, "RegionMinChargeIn": {"properties": {"region_id": {"title": "Region Id", "type": "integer"}, "min_charge": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Min Charge"}, "min_items": {"title": "Min Items", "type": "integer"}}, "required": ["region_id", "min_charge", "min_items"], "title": "RegionMinChargeIn", "type": "object"}, "ItemOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "product": {"$ref": "#/components/schemas/ProductOut"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Base Price"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "price_expiry": {"format": "date-time", "title": "Price Expiry", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}}, "required": ["id", "product", "base_price", "inventory_count", "price_expiry", "created_at"], "title": "ItemOut", "type": "object"}, "ItemIn": {"properties": {"product_id": {"title": "Product Id", "type": "integer"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Base Price"}, "inventory_count": {"default": 0, "title": "Inventory Count", "type": "integer"}, "price_expiry": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Price Expiry"}}, "required": ["product_id", "base_price"], "title": "ItemIn", "type": "object"}, "ItemUpdate": {"properties": {"base_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Base Price"}, "inventory_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Inventory Count"}, "price_expiry": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Price Expiry"}}, "title": "ItemUpdate", "type": "object"}, "InventoryUpdateResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "message": {"title": "Message", "type": "string"}, "new_inventory_count": {"title": "New Inventory Count", "type": "integer"}}, "required": ["success", "message", "new_inventory_count"], "title": "InventoryUpdateResponse", "type": "object"}, "InventoryTransactionIn": {"properties": {"transaction_type": {"title": "Transaction Type", "type": "string"}, "quantity": {"title": "Quantity", "type": "integer"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "required": ["transaction_type", "quantity"], "title": "InventoryTransactionIn", "type": "object"}, "InventoryTransactionOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "transaction_type": {"title": "Transaction Type", "type": "string"}, "quantity": {"title": "Quantity", "type": "integer"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}}, "required": ["id", "transaction_type", "quantity", "created_at"], "title": "InventoryTransactionOut", "type": "object"}, "RegisterResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}, "message": {"title": "Message", "type": "string"}, "token": {"title": "Token", "type": "string"}}, "required": ["success", "user_id", "phone", "message", "token"], "title": "RegisterResponse", "type": "object"}, "RegisterRequest": {"properties": {"name": {"title": "Name", "type": "string"}, "password": {"title": "Password", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}}, "required": ["name", "password", "phone"], "title": "RegisterRequest", "type": "object"}, "PaginatedProductResponse": {"description": "Response model for paginated products", "properties": {"products": {"items": {"$ref": "#/components/schemas/ProductWithPricing"}, "title": "Products", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["products", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedProductResponse", "type": "object"}, "ProductWithPricing": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Unit Count"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Base Price"}, "other_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Other Price"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count"], "title": "ProductWithPricing", "type": "object"}, "ProductResponse": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"title": "Image Url", "type": "string"}, "company_id": {"title": "Company Id", "type": "integer"}, "category_id": {"title": "Category Id", "type": "integer"}, "company": {"$ref": "#/components/schemas/CompanyOut"}, "category": {"$ref": "#/components/schemas/CategoryOut"}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "integer"}, "prices": {"items": {"$ref": "#/components/schemas/ProductPriceInfo"}, "title": "Prices", "type": "array"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "company_id", "category_id", "company", "category", "unit", "unit_count", "prices"], "title": "ProductResponse", "type": "object"}, "WholesalerMinChargeOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "min_charge": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Min Charge"}, "min_items": {"title": "Min Items", "type": "integer"}}, "required": ["id", "min_charge", "min_items"], "title": "WholesalerMinChargeOut", "type": "object"}, "WholesalerItemOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "product_id": {"title": "Product Id", "type": "integer"}, "product_name": {"title": "Product Name", "type": "string"}, "price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Price"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "price_expiry": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Price Expiry"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "integer"}}, "required": ["id", "product_id", "product_name", "price", "inventory_count", "unit", "unit_count"], "title": "WholesalerItemOut", "type": "object"}, "CustomUserOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "username": {"title": "Username", "type": "string"}, "email": {"title": "Email", "type": "string"}, "first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}}, "required": ["id", "username", "email", "first_name", "last_name"], "title": "CustomUserOut", "type": "object"}, "StoreOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "owner": {"$ref": "#/components/schemas/CustomUserOut"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "city": {"$ref": "#/components/schemas/RegionOut"}, "state": {"$ref": "#/components/schemas/RegionOut"}, "country": {"$ref": "#/components/schemas/RegionOut"}, "created_at": {"title": "Created At", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string"}}, "required": ["id", "owner", "name", "description", "address", "city", "state", "country", "created_at", "updated_at"], "title": "StoreOut", "type": "object"}, "StoreIn": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "city_id": {"title": "City Id", "type": "integer"}, "state_id": {"title": "State Id", "type": "integer"}, "country_id": {"title": "Country Id", "type": "integer"}}, "required": ["name", "description", "address", "city_id", "state_id", "country_id"], "title": "StoreIn", "type": "object"}, "OrderItemOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "product": {"$ref": "#/components/schemas/ProductBaseOut"}, "quantity": {"title": "Quantity", "type": "integer"}, "price_per_unit": {"title": "Price Per Unit", "type": "number"}, "total_price": {"title": "Total Price", "type": "number"}}, "required": ["id", "product", "quantity", "price_per_unit", "total_price"], "title": "OrderItemOut", "type": "object"}, "OrderOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "store_id": {"title": "Store Id", "type": "integer"}, "store_name": {"title": "Store Name", "type": "string"}, "total_price": {"title": "Total Price", "type": "number"}, "fees": {"title": "Fees", "type": "number"}, "deliver_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deliver At"}, "products_total_price": {"title": "Products Total Price", "type": "number"}, "products_total_quantity": {"title": "Products Total Quantity", "type": "integer"}, "status": {"title": "Status", "type": "string"}, "status_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Reason"}, "status_updated_at": {"title": "Status Updated At", "type": "string"}, "status_updated_by": {"$ref": "#/components/schemas/CustomUserOut"}, "created_at": {"title": "Created At", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string"}, "order_items": {"items": {"$ref": "#/components/schemas/OrderItemOut"}, "title": "Order Items", "type": "array"}}, "required": ["id", "wholesaler_id", "store_id", "store_name", "total_price", "fees", "deliver_at", "products_total_price", "products_total_quantity", "status", "status_reason", "status_updated_at", "status_updated_by", "created_at", "updated_at", "order_items"], "title": "OrderOut", "type": "object"}, "ProductBaseOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "integer"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "image_url", "unit", "unit_count"], "title": "ProductBaseOut", "type": "object"}, "OrderIn": {"properties": {"wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "store_id": {"title": "Store Id", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/OrderItemIn"}, "title": "Items", "type": "array"}, "deliver_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deliver At"}}, "required": ["wholesaler_id", "store_id", "items"], "title": "OrderIn", "type": "object"}, "OrderItemIn": {"properties": {"item_id": {"title": "Item Id", "type": "integer"}, "quantity": {"title": "Quantity", "type": "integer"}}, "required": ["item_id", "quantity"], "title": "OrderItemIn", "type": "object"}, "PaginatedOrderResponse": {"properties": {"total": {"title": "Total", "type": "integer"}, "items": {"items": {"$ref": "#/components/schemas/OrderOut"}, "title": "Items", "type": "array"}}, "required": ["total", "items"], "title": "PaginatedOrderResponse", "type": "object"}, "OrderStatusUpdateIn": {"properties": {"status": {"title": "Status", "type": "string"}, "status_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Reason"}}, "required": ["status"], "title": "OrderStatusUpdateIn", "type": "object"}, "CategoryCompanyItemOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}}, "required": ["id", "name"], "title": "CategoryCompanyItemOut", "type": "object"}, "CategoryCompanyOut": {"properties": {"categories": {"items": {"$ref": "#/components/schemas/CategoryCompanyItemOut"}, "title": "Categories", "type": "array"}, "companies": {"items": {"$ref": "#/components/schemas/CategoryCompanyItemOut"}, "title": "Companies", "type": "array"}}, "required": ["categories", "companies"], "title": "CategoryCompanyOut", "type": "object"}}, "securitySchemes": {"AuthBearerMiddleware": {"type": "http", "scheme": "bearer"}}}, "servers": []}