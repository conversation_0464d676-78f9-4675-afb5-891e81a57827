import '../api/auth/user.dart';

class UserService {
  static UserData? _cachedUserData;

  /// Get current user data from cache or API
  static Future<UserData?> getCurrentUser({bool forceRefresh = false}) async {
    if (_cachedUserData != null && !forceRefresh) {
      return _cachedUserData;
    }

    final response = await UserApi.getCurrentUser();
    if (response.success && response.user != null) {
      _cachedUserData = response.user;
      return _cachedUserData;
    }

    return null;
  }

  /// Update user data
  static Future<UserResponse> updateCurrentUser(
      UserUpdateRequest request) async {
    final response = await UserApi.updateCurrentUser(request);

    if (response.success && response.user != null) {
      // Update cache with new data
      _cachedUserData = response.user;
    }

    return response;
  }

  /// Clear cached user data (useful for logout)
  static void clearCache() {
    _cachedUserData = null;
  }

  /// Get user's display name
  static String getDisplayName() {
    return _cachedUserData?.fullName ?? 'User';
  }

  /// Check if phone is verified
  static bool isPhoneVerified() {
    return _cachedUserData?.phoneVerified ?? false;
  }
}
