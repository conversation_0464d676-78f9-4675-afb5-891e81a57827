import 'package:shared_preferences/shared_preferences.dart';
import '../api/api.dart';
import '../api/auth/login.dart';
import '../api/auth/signup.dart';
import 'user_service.dart';

enum UserType {
  customer,
  wholesaler,
}

extension UserTypeExtension on UserType {
  String get value => switch (this) {
        UserType.customer => 'customer',
        UserType.wholesaler => 'wholesaler',
      };
}

// Service Response Models
class AuthResult {
  final bool success;
  final String? userType;
  final String? error;
  final LoginResponse? loginData;
  final SignupResponse? signupData;

  AuthResult({
    required this.success,
    this.userType,
    this.error,
    this.loginData,
    this.signupData,
  });

  factory AuthResult.loginSuccess(LoginResponse loginResponse) {
    return AuthResult(
      success: true,
      userType: loginResponse.userType,
      loginData: loginResponse,
    );
  }

  factory AuthResult.signupSuccess(SignupResponse signupResponse) {
    return AuthResult(
      success: true,
      userType:
          UserType.customer.value, // New registrations default to customer
      signupData: signupResponse,
    );
  }

  factory AuthResult.error(String errorMessage) {
    return AuthResult(
      success: false,
      error: errorMessage,
    );
  }
}

class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static const String _phoneKey = 'phone';
  static const String _userTypeKey = 'user_type';
  static const String _wholesalerIdKey = 'wholesaler_id';

  // Login method
  static Future<AuthResult> login({
    required String phone,
    required String password,
  }) async {
    final loginResponse =
        await LoginApi.login(phone: phone, password: password);

    if (loginResponse.success && loginResponse.token != null) {
      if (loginResponse.userType == UserType.wholesaler.value) {
        await _storeUserType(UserType.wholesaler.value);
      } else {
        await _storeUserType(UserType.customer.value);
      }

      // Store authentication data
      await _storeAuthData(
        token: loginResponse.token!,
        userId: loginResponse.userId!,
        phone: loginResponse.phone!,
        wholesalerId: loginResponse.wholesalerId,
      );

      // Store user type
      await _storeUserType(loginResponse.userType);

      return AuthResult.loginSuccess(loginResponse);
    }

    return AuthResult.error(loginResponse.error ?? 'Login failed');
  }

  // Signup method
  static Future<AuthResult> signup({
    required String name,
    required String phone,
    required String password,
    String? email,
  }) async {
    final signupResponse = await SignupApi.signup(
      name: name,
      phone: phone,
      password: password,
      email: email,
    );

    if (signupResponse.success) {
      // For new registrations, default to customer type
      await _storeUserType(UserType.customer.value);
      await _storeAuthData(
        token: signupResponse.token!,
        userId: signupResponse.userId!,
        phone: signupResponse.phone!,
        wholesalerId: null,
      );

      return AuthResult.signupSuccess(signupResponse);
    }

    return AuthResult.error(signupResponse.error ?? 'Registration failed');
  }

  // Store authentication data
  static Future<void> _storeAuthData({
    required String token,
    required int userId,
    required String phone,
    int? wholesalerId,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
    await prefs.setInt(_userIdKey, userId);
    await prefs.setString(_phoneKey, phone);

    if (wholesalerId != null) {
      await prefs.setInt(_wholesalerIdKey, wholesalerId);
    }

    HttpService.instance.setToken(token);
  }

  // Store user type
  static Future<void> _storeUserType(String userType) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userTypeKey, userType);
  }

  // Get stored token
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  // Get user type
  static Future<String?> getUserType() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userTypeKey);
  }

  // Get user ID
  static Future<int?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_userIdKey);
  }

  // Get wholesaler ID
  static Future<int?> getWholesalerId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_wholesalerIdKey);
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Check if user is wholesaler
  static Future<bool> isWholesaler() async {
    final userType = await getUserType();
    return userType == UserType.wholesaler.value;
  }

  // Logout
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userIdKey);
    await prefs.remove(_phoneKey);
    await prefs.remove(_userTypeKey);
    await prefs.remove(_wholesalerIdKey);

    // Clear user data cache
    UserService.clearCache();
    HttpService.instance.clearToken();
  }
}
