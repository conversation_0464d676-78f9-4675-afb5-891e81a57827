import 'region_service.dart';
import 'cart_service.dart';

/// Global service locator for app-wide services
class AppServices {
  static final AppServices _instance = AppServices._internal();
  factory AppServices() => _instance;
  AppServices._internal();

  // Service instances
  RegionService? _regionService;
  CartService? _cartService;

  /// Get the global RegionService instance
  RegionService get regionService {
    _regionService ??= RegionService();
    return _regionService!;
  }

  /// Get the global CartService instance
  CartService get cartService {
    _cartService ??= CartService();
    return _cartService!;
  }

  /// Initialize all services
  Future<void> initialize() async {
    await regionService.initialize();
    await cartService.initialize();
  }

  /// Clear all services (for logout/reset)
  Future<void> clearAllServices() async {
    await regionService.clearAllData();
    await cartService.clearCart();
    _regionService = null;
    _cartService = null;
  }
}
