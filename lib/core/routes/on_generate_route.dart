import 'package:flutter/cupertino.dart';

import '../../views/auth/forget_password_page.dart';
import '../../views/auth/intro_login_page.dart';
import '../../views/auth/login_or_signup_page.dart';
import '../../views/auth/login_page.dart';
import '../../views/auth/number_verification_page.dart';
import '../../views/auth/password_reset_page.dart';
import '../../views/auth/sign_up_page.dart';
import '../../views/cart/cart_page.dart';
import '../../views/cart/checkout_page.dart';
import '../../views/drawer/about_us_page.dart';
import '../../views/drawer/contact_us_page.dart';
import '../../views/drawer/drawer_page.dart';
import '../../views/drawer/faq_page.dart';
import '../../views/drawer/help_page.dart';
import '../../views/drawer/terms_and_conditions_page.dart';
import '../../views/entrypoint/entrypoint_ui.dart';
import '../../views/entrypoint/wholesaler_entrypoint_ui.dart';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/home/<USER>';
import '../../views/menu/category_page.dart';
import '../../views/onboarding/onboarding_page.dart';
import '../../views/profile/address/address_page.dart';
import '../../views/profile/address/new_address_page.dart';
import '../../views/profile/coupon/coupon_details_page.dart';
import '../../views/profile/coupon/coupon_page.dart';
import '../../views/profile/notification_page.dart';
import '../../views/profile/order/my_order_page.dart';
import '../../views/profile/order/order_details.dart';
import '../../views/profile/payment_method/add_new_card_page.dart';
import '../../views/profile/payment_method/payment_method_page.dart';
import '../../views/profile/profile_edit_page.dart';
import '../../views/profile/settings/change_password_page.dart';
import '../../views/profile/settings/change_phone_number_page.dart';
import '../../views/profile/settings/language_settings_page.dart';
import '../../views/profile/settings/notifications_settings_page.dart';
import '../../views/profile/settings/settings_page.dart';
import '../../views/review/review_page.dart';
import '../../views/review/submit_review_page.dart';
import '../../views/save/save_page.dart';
import '../../views/splash/splash_screen.dart';
import '../../views/wholesaler/wholesaler_home_page.dart';
import '../../views/wholesaler/pages/item_form_page.dart';
import '../../views/wholesaler/pages/item_list_page.dart';
import '../../views/wholesaler/pages/inventory_management_page.dart';
import '../../views/wholesaler/pages/region_pricing_page.dart';
import '../../views/wholesaler/pages/orders_listing_page.dart';
import '../../views/wholesaler/pages/order_details_page.dart';
import '../../views/home/<USER>';
import '../../views/profile/stores/stores_list_page.dart';
import '../../views/profile/stores/store_form_page.dart';
import '../../models/store_models.dart';
import 'app_routes.dart';
import 'unknown_page.dart';

class RouteGenerator {
  static Route? onGenerate(RouteSettings settings) {
    final route = settings.name;

    switch (route) {
      case AppRoutes.splash:
        return CupertinoPageRoute(builder: (_) => const SplashScreen());

      case AppRoutes.introLogin:
        return CupertinoPageRoute(builder: (_) => const IntroLoginPage());

      case AppRoutes.onboarding:
        return CupertinoPageRoute(builder: (_) => const OnboardingPage());

      case AppRoutes.entryPoint:
        return CupertinoPageRoute(builder: (_) => const EntryPointUI());

      case AppRoutes.wholesalerHome:
        return CupertinoPageRoute(builder: (_) => const WholesalerHomePage());

      case AppRoutes.wholesalerEntrypoint:
        return CupertinoPageRoute(
            builder: (_) => const WholesalerEntryPointUI());

      case AppRoutes.search:
        return CupertinoPageRoute(builder: (_) => const SearchPage());

      case AppRoutes.searchResult:
        return CupertinoPageRoute(builder: (_) => const SearchResultPage());

      case AppRoutes.cartPage:
        return CupertinoPageRoute(builder: (_) => const CartPage());

      case AppRoutes.savePage:
        return CupertinoPageRoute(builder: (_) => const SavePage());

      case AppRoutes.checkoutPage:
        return CupertinoPageRoute(builder: (_) => const CheckoutPage());

      case AppRoutes.categoryDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        final id = args?['id'] as int?;
        final type = args?['type'] as String?;
        final name = args?['name'] as String?;
        if (id == null || type == null || name == null) {
          return errorRoute();
        }
        return CupertinoPageRoute(
          builder: (_) => CategoryProductPage(id: id, type: type, name: name),
        );

      case AppRoutes.login:
        return CupertinoPageRoute(builder: (_) => const LoginPage());

      case AppRoutes.signup:
        return CupertinoPageRoute(builder: (_) => const SignUpPage());

      case AppRoutes.loginOrSignup:
        return CupertinoPageRoute(builder: (_) => const LoginOrSignUpPage());

      case AppRoutes.numberVerification:
        return CupertinoPageRoute(
            builder: (_) => const NumberVerificationPage());

      case AppRoutes.forgotPassword:
        return CupertinoPageRoute(builder: (_) => const ForgetPasswordPage());

      case AppRoutes.passwordReset:
        return CupertinoPageRoute(builder: (_) => const PasswordResetPage());

      case AppRoutes.newItems:
        return CupertinoPageRoute(builder: (_) => const NewItemsPage());

      case AppRoutes.popularItems:
        return CupertinoPageRoute(builder: (_) => const PopularPackPage());

      case AppRoutes.bundleProduct:
        return CupertinoPageRoute(
            builder: (_) => const BundleProductDetailsPage());

      case AppRoutes.bundleDetailsPage:
        return CupertinoPageRoute(builder: (_) => const BundleDetailsPage());

      case AppRoutes.productDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        final productId = args?['productId'] as int?;
        final wholesalerMode = args?['wholesalerMode'] as bool? ?? false;
        final wholesalerId = args?['wholesalerId'] as int?;
        final wholesalerItemId = args?['wholesalerItemId'] as int?;
        final wholesalerInfo = args?['wholesalerInfo'];
        return CupertinoPageRoute(
          builder: (_) => ProductDetailsPage(
            productId: productId,
            wholesalerMode: wholesalerMode,
            wholesalerId: wholesalerId,
            wholesalerItemId: wholesalerItemId,
            wholesalerInfo: wholesalerInfo,
          ),
        );

      case AppRoutes.createMyPack:
        return CupertinoPageRoute(builder: (_) => const BundleCreatePage());

      case AppRoutes.orderSuccessfull:
        return CupertinoPageRoute(builder: (_) => const OrderSuccessfullPage());

      case AppRoutes.orderFailed:
        return CupertinoPageRoute(builder: (_) => const OrderFailedPage());

      case AppRoutes.myOrder:
        return CupertinoPageRoute(builder: (_) => const AllOrderPage());

      case AppRoutes.orderDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        final orderId = args?['orderId'] as int?;
        final storeId = args?['storeId'] as int?;

        if (orderId == null || storeId == null) {
          return errorRoute();
        }

        return CupertinoPageRoute(
          builder: (_) => OrderDetailsPage(
            orderId: orderId,
            storeId: storeId,
          ),
        );

      case AppRoutes.coupon:
        return CupertinoPageRoute(builder: (_) => const CouponAndOffersPage());

      case AppRoutes.couponDetails:
        return CupertinoPageRoute(builder: (_) => const CouponDetailsPage());

      case AppRoutes.profileEdit:
        return CupertinoPageRoute(builder: (_) => const ProfileEditPage());

      case AppRoutes.newAddress:
        return CupertinoPageRoute(builder: (_) => const NewAddressPage());

      case AppRoutes.deliveryAddress:
        return CupertinoPageRoute(builder: (_) => const AddressPage());

      case AppRoutes.notifications:
        return CupertinoPageRoute(builder: (_) => const NotificationPage());

      case AppRoutes.settingsNotifications:
        return CupertinoPageRoute(
            builder: (_) => const NotificationSettingsPage());

      case AppRoutes.settings:
        return CupertinoPageRoute(builder: (_) => const SettingsPage());

      case AppRoutes.settingsLanguage:
        return CupertinoPageRoute(builder: (_) => const LanguageSettingsPage());

      case AppRoutes.changePassword:
        return CupertinoPageRoute(builder: (_) => const ChangePasswordPage());

      case AppRoutes.changePhoneNumber:
        return CupertinoPageRoute(
            builder: (_) => const ChangePhoneNumberPage());

      case AppRoutes.review:
        return CupertinoPageRoute(builder: (_) => const ReviewPage());

      case AppRoutes.submitReview:
        return CupertinoPageRoute(builder: (_) => const SubmitReviewPage());

      case AppRoutes.drawerPage:
        return CupertinoPageRoute(builder: (_) => const DrawerPage());

      case AppRoutes.aboutUs:
        return CupertinoPageRoute(builder: (_) => const AboutUsPage());

      case AppRoutes.termsAndConditions:
        return CupertinoPageRoute(
            builder: (_) => const TermsAndConditionsPage());

      case AppRoutes.faq:
        return CupertinoPageRoute(builder: (_) => const FAQPage());

      case AppRoutes.help:
        return CupertinoPageRoute(builder: (_) => const HelpPage());

      case AppRoutes.contactUs:
        return CupertinoPageRoute(builder: (_) => const ContactUsPage());

      case AppRoutes.paymentMethod:
        return CupertinoPageRoute(builder: (_) => const PaymentMethodPage());

      case AppRoutes.paymentCardAdd:
        return CupertinoPageRoute(builder: (_) => const AddNewCardPage());

      case AppRoutes.wholesalerProducts:
        final args = settings.arguments as Map<String, dynamic>?;
        final wholesalerId = args?['wholesalerId'] as int?;
        if (wholesalerId == null) {
          return errorRoute();
        }
        return CupertinoPageRoute(
          builder: (_) => WholesalerProductsPage(wholesalerId: wholesalerId),
        );

      case AppRoutes.storesList:
        return CupertinoPageRoute(builder: (_) => const StoresListPage());

      case AppRoutes.createStore:
        return CupertinoPageRoute(builder: (_) => const StoreFormPage());

      case AppRoutes.editStore:
        final args = settings.arguments as StoreData?;
        return CupertinoPageRoute(
          builder: (_) => StoreFormPage(store: args),
        );

      case AppRoutes.wholesalerItemList:
        return CupertinoPageRoute(
            builder: (_) => const ItemListPage(
                  canPop: true,
                ));

      case AppRoutes.wholesalerItemForm:
        final args = settings.arguments as Map<String, dynamic>?;
        final item = args?['item'];
        return CupertinoPageRoute(
          builder: (_) => ItemFormPage(item: item),
        );

      case AppRoutes.wholesalerInventoryManagement:
        return CupertinoPageRoute(
            builder: (_) => const InventoryManagementPage());

      case AppRoutes.wholesalerRegionPricing:
        return CupertinoPageRoute(builder: (_) => const RegionPricingPage());

      case AppRoutes.wholesalerOrdersList:
        return CupertinoPageRoute(
            builder: (_) => const WholesalerOrdersListingPage());

      case AppRoutes.wholesalerOrderDetails:
        final args = settings.arguments as Map<String, dynamic>?;
        final orderId = args?['orderId'] as int?;
        if (orderId == null) {
          return errorRoute();
        }
        return CupertinoPageRoute(
          builder: (_) => WholesalerOrderDetailsPage(orderId: orderId),
        );

      default:
        return errorRoute();
    }
  }

  static Route? errorRoute() =>
      CupertinoPageRoute(builder: (_) => const UnknownPage());
}
