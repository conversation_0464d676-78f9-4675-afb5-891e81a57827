import 'package:flutter/material.dart';

import '../constants/constants.dart';
import '../models/dummy_product_model.dart';
import '../routes/app_routes.dart';
import '../utils/currency_formatter.dart';
import 'network_image.dart';

class ProductTileSquare extends StatelessWidget {
  const ProductTileSquare({
    super.key,
    required this.data,
    this.productId,
  });

  final ProductModel data;
  final int? productId;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding / 2),
      child: Material(
        borderRadius: AppDefaults.borderRadius,
        color: AppColors.scaffoldBackground,
        child: InkWell(
          borderRadius: AppDefaults.borderRadius,
          onTap: () {
            if (productId != null) {
              Navigator.pushNamed(
                context,
                AppRoutes.productDetails,
                arguments: {'productId': productId},
              );
            } else {
              // Fallback to old navigation for dummy data
              Navigator.pushNamed(context, AppRoutes.productDetails);
            }
          },
          child: Container(
            width: 176,
            height: 296,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(width: 0.1, color: AppColors.placeholder),
              borderRadius: AppDefaults.borderRadius,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding / 2),
                  child: AspectRatio(
                    aspectRatio: 1 / 1,
                    child: NetworkImageWithLoader(
                      data.cover,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  data.name,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(color: Colors.black),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Spacer(),
                Text(
                  data.weight,
                ),
                const SizedBox(height: 4),
                _buildPriceSection(context)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSection(BuildContext context) {
    // Check if we have valid prices to display
    final hasValidPrice = CurrencyFormatter.isValidPrice(data.price);
    final hasValidMainPrice = CurrencyFormatter.isValidPrice(data.mainPrice);

    if (!hasValidPrice && !hasValidMainPrice) {
      return Text(
        'السعر غير متوفر',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
      );
    }

    // If we only have one price or both prices are the same, show only one
    if (!hasValidMainPrice || data.price == data.mainPrice) {
      return Text(
        CurrencyFormatter.formatPriceForTile(data.price),
        style: Theme.of(context)
            .textTheme
            .titleLarge
            ?.copyWith(color: Colors.black),
      );
    }

    // Show both prices with strikethrough for the higher price
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          CurrencyFormatter.formatPriceForTile(data.price),
          style: Theme.of(context)
              .textTheme
              .titleLarge
              ?.copyWith(color: Colors.black),
        ),
        const SizedBox(width: 4),
        Text(
          CurrencyFormatter.formatPriceForTile(data.mainPrice),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                decoration: TextDecoration.lineThrough,
                color: Colors.grey,
              ),
        ),
      ],
    );
  }
}
