import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/cart_models.dart';
import 'api.dart';

/// Models for Wholesaler API responses
class WholesalerItemResponse {
  final int id;
  final int productId;
  final String productName;
  final double price;
  final int inventoryCount;
  final DateTime? priceExpiry;
  final String? imageUrl;
  final int? companyId;
  final int? categoryId;
  final CompanyOut? company;
  final CategoryOut? category;
  final String unit;
  final int unitCount;

  WholesalerItemResponse({
    required this.id,
    required this.productId,
    required this.productName,
    required this.price,
    required this.inventoryCount,
    this.priceExpiry,
    this.imageUrl,
    this.companyId,
    this.categoryId,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
  });

  factory WholesalerItemResponse.fromJson(Map<String, dynamic> json) {
    return WholesalerItemResponse(
      id: json['id'],
      productId: json['product_id'],
      productName: json['product_name'],
      price: double.parse(json['price'].toString()),
      inventoryCount: json['inventory_count'],
      priceExpiry: json['price_expiry'] != null
          ? DateTime.parse(json['price_expiry'])
          : null,
      imageUrl: json['image_url'],
      companyId: json['company_id'],
      categoryId: json['category_id'],
      company:
          json['company'] != null ? CompanyOut.fromJson(json['company']) : null,
      category: json['category'] != null
          ? CategoryOut.fromJson(json['category'])
          : null,
      unit: json['unit'],
      unitCount: json['unit_count'],
    );
  }
}

class CompanyOut {
  final int id;
  final String name;
  final String title;
  final String slug;

  CompanyOut({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CompanyOut.fromJson(Map<String, dynamic> json) {
    return CompanyOut(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      slug: json['slug'],
    );
  }
}

class CategoryOut {
  final int id;
  final String name;
  final String title;
  final String slug;

  CategoryOut({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CategoryOut.fromJson(Map<String, dynamic> json) {
    return CategoryOut(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      slug: json['slug'],
    );
  }
}

class WholesalerMinChargeResponse {
  final int id;
  final double minCharge;
  final int minItems;

  WholesalerMinChargeResponse({
    required this.id,
    required this.minCharge,
    required this.minItems,
  });

  factory WholesalerMinChargeResponse.fromJson(Map<String, dynamic> json) {
    return WholesalerMinChargeResponse(
      id: json['id'],
      minCharge: double.parse(json['min_charge'].toString()),
      minItems: json['min_items'],
    );
  }
}

/// Exception classes for error handling
class WholesalerApiException implements Exception {
  final String message;
  final int? statusCode;

  WholesalerApiException(this.message, [this.statusCode]);

  @override
  String toString() => 'WholesalerApiException: $message';
}

class WholesalerApiService {
  static const String _baseEndpoint = '/api/v2/wholesalers';

  /// Get wholesaler information by ID
  static Future<WholesalerInfo> getWholesalerById(int wholesalerId) async {
    try {
      final url = '$_baseEndpoint/$wholesalerId';
      final response = await HttpService.instance.get(url);
      return WholesalerInfo.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get minimum charge requirements for a wholesaler in a specific region
  static Future<WholesalerMinChargeResponse> getWholesalerMinCharge(
      int wholesalerId, int regionId) async {
    try {
      final url = '$_baseEndpoint/$wholesalerId/min-charge/$regionId';
      final response = await HttpService.instance.get(url);
      return WholesalerMinChargeResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get all items (products) for a specific wholesaler
  static Future<List<WholesalerItemResponse>> getWholesalerItems(
      int wholesalerId) async {
    try {
      final url = '$_baseEndpoint/$wholesalerId/items';
      final response = await HttpService.instance.get(url);

      if (response is List) {
        return response
            .map((item) => WholesalerItemResponse.fromJson(item))
            .toList();
      } else {
        throw WholesalerApiException('Invalid response format');
      }
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get a specific item from a wholesaler's inventory
  static Future<WholesalerItemResponse> getWholesalerItemById(
      int wholesalerItemId) async {
    try {
      final url = '$_baseEndpoint/items/$wholesalerItemId';
      final response = await HttpService.instance.get(url);
      return WholesalerItemResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw WholesalerApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Handle API errors and convert them to user-friendly messages
  static WholesalerApiException _handleApiError(DioException e) {
    String message;
    int? statusCode = e.response?.statusCode;

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        message = 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.';
        break;
      case DioExceptionType.badResponse:
        switch (statusCode) {
          case 404:
            message = 'التاجر أو المنتج غير موجود.';
            break;
          case 401:
            message = 'يجب تسجيل الدخول للوصول إلى هذه المعلومات.';
            break;
          case 403:
            message = 'ليس لديك صلاحية للوصول إلى هذه المعلومات.';
            break;
          case 500:
            message = 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
            break;
          default:
            message = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
        }
        break;
      case DioExceptionType.cancel:
        message = 'تم إلغاء الطلب.';
        break;
      case DioExceptionType.badCertificate:
        message = 'خطأ في شهادة الأمان.';
        break;
      case DioExceptionType.connectionError:
        message = 'خطأ في الاتصال. يرجى التحقق من اتصال الإنترنت.';
        break;
      default:
        message = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }

    return WholesalerApiException(message, statusCode);
  }
}
