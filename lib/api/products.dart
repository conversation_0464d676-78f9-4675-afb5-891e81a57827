import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'api.dart';

// Models for Product Details API Response
class WholesalerOut {
  final int id;
  final String category;
  final String title;
  final String username;
  final String? backgroundImage;
  final String? logo;

  WholesalerOut({
    required this.id,
    required this.category,
    required this.title,
    required this.username,
    this.backgroundImage,
    this.logo,
  });

  factory WholesalerOut.fromJson(Map<String, dynamic> json) {
    return WholesalerOut(
      id: json['id'],
      category: json['category'],
      title: json['title'],
      username: json['username'],
      backgroundImage: json['background_image_url'],
      logo: json['logo_url'],
    );
  }
}

class ProductPriceInfo {
  final double price;
  final int wholesalerId;
  final WholesalerOut wholesaler;
  final int inventoryCount;
  final DateTime? priceExpiry;
  final int itemId;

  ProductPriceInfo({
    required this.price,
    required this.wholesalerId,
    required this.wholesaler,
    required this.inventoryCount,
    this.priceExpiry,
    required this.itemId,
  });

  factory ProductPriceInfo.fromJson(Map<String, dynamic> json) {
    return ProductPriceInfo(
      price: double.parse(json['price'].toString()),
      wholesalerId: json['wholesaler_id'],
      wholesaler: WholesalerOut.fromJson(json['wholesaler']),
      inventoryCount: json['inventory_count'],
      priceExpiry: json['price_expiry'] != null
          ? DateTime.parse(json['price_expiry'])
          : null,
      itemId: json['item_id'],
    );
  }
}

class CompanyOut {
  final int id;
  final String name;
  final String title;
  final String slug;

  CompanyOut({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CompanyOut.fromJson(Map<String, dynamic> json) {
    return CompanyOut(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      slug: json['slug'],
    );
  }
}

class CategoryOut {
  final int id;
  final String name;
  final String title;
  final String slug;

  CategoryOut({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CategoryOut.fromJson(Map<String, dynamic> json) {
    return CategoryOut(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      slug: json['slug'],
    );
  }
}

class ProductDetailResponse {
  final int id;
  final String name;
  final String title;
  final String barcode;
  final String slug;
  final String description;
  final String? imageUrl;
  final int? companyId;
  final int? categoryId;
  final CompanyOut? company;
  final CategoryOut? category;
  final String unit;
  final int unitCount;
  final List<ProductPriceInfo> prices;

  ProductDetailResponse({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.companyId,
    this.categoryId,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
    required this.prices,
  });

  factory ProductDetailResponse.fromJson(Map<String, dynamic> json) {
    return ProductDetailResponse(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      barcode: json['barcode'],
      slug: json['slug'],
      description: json['description'],
      imageUrl: json['image_url'],
      companyId: json['company_id'],
      categoryId: json['category_id'],
      company:
          json['company'] != null ? CompanyOut.fromJson(json['company']) : null,
      category: json['category'] != null
          ? CategoryOut.fromJson(json['category'])
          : null,
      unit: json['unit'],
      unitCount: json['unit_count'],
      prices: (json['prices'] as List)
          .map((price) => ProductPriceInfo.fromJson(price))
          .toList(),
    );
  }

  // Helper methods for price display
  double? get minPrice {
    if (prices.isEmpty) return null;
    return prices.map((p) => p.price).reduce((a, b) => a < b ? a : b);
  }

  double? get maxPrice {
    if (prices.isEmpty) return null;
    return prices.map((p) => p.price).reduce((a, b) => a > b ? a : b);
  }

  List<String> get imageUrls {
    return imageUrl != null ? [imageUrl!] : [];
  }
}

class ProductDetailsApiService {
  static const String _baseEndpoint = '/api/v2/products';

  /// Get product details by ID with prices from all wholesalers in the region
  static Future<ProductDetailResponse> getProductById(
    int productId, {
    int regionId = 1, // Default region ID, should be configurable
  }) async {
    try {
      final url = '$_baseEndpoint/$productId?region_id=$regionId';
      final response = await HttpService.instance.get(url);
      return ProductDetailResponse.fromJson(response);
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw ProductDetailsApiException(
          'Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get products by category ID
  static Future<List<ProductDetailResponse>> getProductsByCategoryId(
    int categoryId, {
    int regionId = 1, // Default region ID, should be configurable
  }) async {
    try {
      final url = '$_baseEndpoint/category/$categoryId?region_id=$regionId';
      final response = await HttpService.instance.get(url);
      return (response as List)
          .map((item) => ProductDetailResponse.fromJson(item))
          .toList();
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw ProductDetailsApiException(
          'Unexpected error occurred:  [${e.toString()}');
    }
  }

  /// Get products by company ID
  static Future<List<ProductDetailResponse>> getProductsByCompanyId(
    int companyId, {
    int regionId = 1, // Default region ID, should be configurable
  }) async {
    try {
      final url = '$_baseEndpoint/company/$companyId?region_id=$regionId';
      final response = await HttpService.instance.get(url);
      return (response as List)
          .map((item) => ProductDetailResponse.fromJson(item))
          .toList();
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw ProductDetailsApiException(
          'Unexpected error occurred:  [${e.toString()}');
    }
  }

  /// Handle API errors and convert them to user-friendly messages
  static ProductDetailsApiException _handleApiError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return ProductDetailsApiException(
            'Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['message'] ?? 'Server error occurred';

        switch (statusCode) {
          case 400:
            return ProductDetailsApiException('Invalid request: $message');
          case 401:
            return ProductDetailsApiException(
                'Authentication required. Please log in again.');
          case 403:
            return ProductDetailsApiException('Access denied: $message');
          case 404:
            return ProductDetailsApiException('Product not found');
          case 500:
            return ProductDetailsApiException(
                'Server error. Please try again later.');
          default:
            return ProductDetailsApiException('Error $statusCode: $message');
        }

      case DioExceptionType.cancel:
        return ProductDetailsApiException('Request was cancelled');

      case DioExceptionType.connectionError:
        return ProductDetailsApiException(
            'No internet connection. Please check your network.');

      default:
        return ProductDetailsApiException('Network error: ${error.message}');
    }
  }
}

/// Custom exception for product details API errors
class ProductDetailsApiException implements Exception {
  final String message;

  ProductDetailsApiException(this.message);

  @override
  String toString() => 'ProductDetailsApiException: $message';
}

// Model for category/company item
class CategoryCompanyItem {
  final int id;
  final String name;

  CategoryCompanyItem({required this.id, required this.name});

  factory CategoryCompanyItem.fromJson(Map<String, dynamic> json) {
    return CategoryCompanyItem(
      id: json['id'],
      name: json['name'],
    );
  }
}

// Model for categories and companies response
class CategoryCompanyResponse {
  final List<CategoryCompanyItem> categories;
  final List<CategoryCompanyItem> companies;

  CategoryCompanyResponse({required this.categories, required this.companies});

  factory CategoryCompanyResponse.fromJson(Map<String, dynamic> json) {
    return CategoryCompanyResponse(
      categories: (json['categories'] as List)
          .map((e) => CategoryCompanyItem.fromJson(e))
          .toList(),
      companies: (json['companies'] as List)
          .map((e) => CategoryCompanyItem.fromJson(e))
          .toList(),
    );
  }
}

// Service method to fetch categories and companies
class CategoryCompanyApiService {
  static Future<CategoryCompanyResponse> fetchCategoriesAndCompanies() async {
    final response = await HttpService.instance.getCategoriesAndCompanies();
    return CategoryCompanyResponse.fromJson(response);
  }
}
