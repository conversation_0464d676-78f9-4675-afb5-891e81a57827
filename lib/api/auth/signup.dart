import 'package:dio/dio.dart';
import '../api.dart';

// Response Models
class SignupResponse {
  final bool success;
  final int? userId;
  final String? phone;
  final String? message;
  final String? error;
  final String? token;

  SignupResponse({
    required this.success,
    this.userId,
    this.phone,
    this.message,
    this.error,
    this.token,
  });

  factory SignupResponse.success({
    required int userId,
    required String phone,
    required String message,
  }) {
    return SignupResponse(
      success: true,
      userId: userId,
      phone: phone,
      message: message,
    );
  }

  factory SignupResponse.error(String errorMessage) {
    return SignupResponse(
      success: false,
      error: errorMessage,
    );
  }

  factory SignupResponse.fromJson(Map<String, dynamic> json) {
    return SignupResponse(
      success: json['success'] ?? false,
      userId: json['user_id'],
      phone: json['phone'],
      message: json['message'],
      error: json['error'],
      token: json['token'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      if (userId != null) 'user_id': userId,
      if (phone != null) 'phone': phone,
      if (message != null) 'message': message,
      if (error != null) 'error': error,
      if (token != null) 'token': token,
    };
  }
}

class SignupApi {
  static Future<SignupResponse> signup({
    required String name,
    required String phone,
    required String password,
    String? email,
  }) async {
    try {
      final response = await HttpService.instance.post(
        '/api/v2/register',
        {
          'name': name,
          'phone': phone,
          'password': password,
          if (email != null && email.isNotEmpty) 'email': email,
        },
      );

      // Create success response from API data
      return SignupResponse.fromJson(response);
    } on DioException catch (e) {
      String errorMessage = 'Registration failed';

      if (e.response?.statusCode == 400) {
        if (e.response!.data['error'].contains('phone number already exists')) {
          errorMessage = 'Phone number is already registered';
        } else if (e.response!.data['error']
            .contains('Username already taken')) {
          errorMessage = 'Username is already taken';
        } else {
          errorMessage = e.response!.data['error'];
        }
      } else if (e.response?.statusCode == 500) {
        errorMessage = 'Server error. Please try again later';
      } else if (e.response?.data != null &&
          e.response!.data['error'] != null) {
        errorMessage = e.response!.data['error'];
      }

      return SignupResponse.error(errorMessage);
    } catch (e) {
      return SignupResponse.error(
          'Network error. Please check your connection');
    }
  }
}
