import 'package:dio/dio.dart';
import '../api.dart';

// Response Models
class UserResponse {
  final bool success;
  final UserData? user;
  final String? error;

  UserResponse({
    required this.success,
    this.user,
    this.error,
  });

  factory UserResponse.fromJson(Map<String, dynamic> json) {
    try {
      return UserResponse(
        success: true,
        user: UserData.fromJson(json),
      );
    } catch (e) {
      return UserResponse(
        success: false,
        error: 'Failed to parse user data: $e',
      );
    }
  }

  factory UserResponse.error(String errorMessage) {
    return UserResponse(
      success: false,
      error: errorMessage,
    );
  }
}

class UserData {
  final int id;
  final String username;
  final String? email;
  final String phone;
  final bool phoneVerified;
  final String? firstName;
  final String? lastName;
  final bool isActive;
  final DateTime dateJoined;
  final int? wholesalerId;

  UserData({
    required this.id,
    required this.username,
    this.email,
    required this.phone,
    required this.phoneVerified,
    this.firstName,
    this.lastName,
    required this.isActive,
    required this.dateJoined,
    this.wholesalerId,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id'] as int,
      username: json['username'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String,
      phoneVerified: json['phone_verified'] as bool,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      isActive: json['is_active'] as bool,
      dateJoined: DateTime.parse(json['date_joined'] as String),
      wholesalerId: json['wholesaler_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'phone_verified': phoneVerified,
      'first_name': firstName,
      'last_name': lastName,
      'is_active': isActive,
      'date_joined': dateJoined.toIso8601String(),
      'wholesaler_id': wholesalerId,
    };
  }

  String get fullName {
    final first = firstName ?? '';
    final last = lastName ?? '';
    final name = '$first $last'.trim();
    return name.isEmpty ? username : name;
  }
}

// Request Models
class UserUpdateRequest {
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;

  UserUpdateRequest({
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};

    if (firstName != null) data['first_name'] = firstName;
    if (lastName != null) data['last_name'] = lastName;
    if (email != null) data['email'] = email;
    if (phone != null) data['phone'] = phone;

    return data;
  }
}

// API Service
class UserApi {
  static Future<UserResponse> getCurrentUser() async {
    try {
      final response = await HttpService.instance.get('/api/v2/me');
      return UserResponse.fromJson(response);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return UserResponse.error('Authentication required');
      }
      return UserResponse.error(
          e.response?.data?['detail'] ?? 'Failed to fetch user data');
    } catch (e) {
      return UserResponse.error('Network error: $e');
    }
  }

  static Future<UserResponse> updateCurrentUser(
      UserUpdateRequest request) async {
    try {
      final response =
          await HttpService.instance.put('/api/v2/me', request.toJson());
      return UserResponse.fromJson(response);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return UserResponse.error('Authentication required');
      }
      if (e.response?.statusCode == 400) {
        return UserResponse.error(
            e.response?.data?['detail'] ?? 'Invalid data provided');
      }
      return UserResponse.error(
          e.response?.data?['detail'] ?? 'Failed to update user data');
    } catch (e) {
      return UserResponse.error('Network error: $e');
    }
  }
}
