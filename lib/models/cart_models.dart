class CartItem {
  final int itemId;
  final String productName;
  final double price;
  final int quantity;
  final String? imageUrl;
  final String unit;
  final int unitCount;
  final int wholesalerItemId;
  final DateTime addedAt;

  const CartItem({
    required this.itemId,
    required this.productName,
    required this.price,
    required this.quantity,
    this.imageUrl,
    required this.unit,
    required this.unitCount,
    required this.wholesalerItemId,
    required this.addedAt,
  });

  double get totalPrice => price * quantity;

  CartItem copyWith({
    int? productId,
    String? productName,
    double? price,
    int? quantity,
    String? imageUrl,
    String? unit,
    int? unitCount,
    int? wholesalerItemId,
    DateTime? addedAt,
  }) {
    return CartItem(
      itemId: productId ?? this.itemId,
      productName: productName ?? this.productName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      imageUrl: imageUrl ?? this.imageUrl,
      unit: unit ?? this.unit,
      unitCount: unitCount ?? this.unitCount,
      wholesalerItemId: wholesalerItemId ?? this.wholesalerItemId,
      addedAt: addedAt ?? this.addedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'productId': itemId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'imageUrl': imageUrl,
      'unit': unit,
      'unitCount': unitCount,
      'wholesalerItemId': wholesalerItemId,
      'addedAt': addedAt.toIso8601String(),
    };
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      itemId: json['productId'],
      productName: json['productName'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
      imageUrl: json['imageUrl'],
      unit: json['unit'],
      unitCount: json['unitCount'],
      wholesalerItemId: json['wholesalerItemId'],
      addedAt: DateTime.parse(json['addedAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.wholesalerItemId == wholesalerItemId;
  }

  @override
  int get hashCode => wholesalerItemId.hashCode;
}

class Cart {
  final int wholesalerId;
  final String wholesalerTitle;
  final String? wholesalerLogo;
  final List<CartItem> items;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Cart({
    required this.wholesalerId,
    required this.wholesalerTitle,
    this.wholesalerLogo,
    required this.items,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;
  int get itemCount => items.length;
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);
  double get totalPrice =>
      items.fold(0.0, (sum, item) => sum + item.totalPrice);

  Cart copyWith({
    int? wholesalerId,
    String? wholesalerTitle,
    String? wholesalerLogo,
    List<CartItem>? items,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Cart(
      wholesalerId: wholesalerId ?? this.wholesalerId,
      wholesalerTitle: wholesalerTitle ?? this.wholesalerTitle,
      wholesalerLogo: wholesalerLogo ?? this.wholesalerLogo,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wholesalerId': wholesalerId,
      'wholesalerTitle': wholesalerTitle,
      'wholesalerLogo': wholesalerLogo,
      'items': items.map((item) => item.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      wholesalerId: json['wholesalerId'],
      wholesalerTitle: json['wholesalerTitle'],
      wholesalerLogo: json['wholesalerLogo'],
      items: (json['items'] as List)
          .map((item) => CartItem.fromJson(item))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

class WholesalerInfo {
  final int id;
  final String title;
  final String username;
  final String? logoUrl;
  final String? backgroundImageUrl;

  const WholesalerInfo({
    required this.id,
    required this.title,
    required this.username,
    this.logoUrl,
    this.backgroundImageUrl,
  });

  factory WholesalerInfo.fromJson(Map<String, dynamic> json) {
    return WholesalerInfo(
      id: json['id'],
      title: json['title'],
      username: json['username'],
      logoUrl: json['logo_url'],
      backgroundImageUrl: json['background_image_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'username': username,
      'logo_url': logoUrl,
      'background_image_url': backgroundImageUrl,
    };
  }
}

enum CartOperationResult {
  success,
  wholesalerConflict,
  itemNotFound,
  insufficientInventory,
  error,
}

class CartOperationResponse {
  final CartOperationResult result;
  final String? message;
  final WholesalerInfo? conflictingWholesaler;

  const CartOperationResponse({
    required this.result,
    this.message,
    this.conflictingWholesaler,
  });

  bool get isSuccess => result == CartOperationResult.success;
  bool get hasWholesalerConflict =>
      result == CartOperationResult.wholesalerConflict;
}
