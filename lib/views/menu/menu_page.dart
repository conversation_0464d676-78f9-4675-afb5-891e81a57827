import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/constants/constants.dart';
import '../../core/routes/app_routes.dart';
import '../../core/components/product_tile_square.dart';
import '../../api/home/<USER>';
import '../../api/home/<USER>';

class MenuPage extends StatefulWidget {
  const MenuPage({super.key});

  @override
  State<MenuPage> createState() => _MenuPageState();
}

class _MenuPageState extends State<MenuPage> {
  final TextEditingController _searchController = TextEditingController();
  List<ProductWithPricing> _searchResults = [];
  bool _isLoading = false;
  bool _hasSearched = false;
  String? _error;
  Timer? _debounceTimer;

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _debouncedSearch(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _hasSearched = false;
        _error = null;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await HomeApiService.searchProducts(query.trim());
      setState(() {
        _searchResults = results;
        _hasSearched = true;
        _isLoading = false;
        _error = null; // Clear any previous errors
      });
    } catch (e) {
      setState(() {
        _error = _getErrorMessage(e);
        _isLoading = false;
        _hasSearched = true;
        _searchResults = []; // Clear results on error
      });
    }
  }

  void _navigateToSearchResult(String query) {
    Navigator.pushNamed(
      context,
      AppRoutes.searchResult,
      arguments: {'query': query, 'results': _searchResults},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.scaffoldBackground,
        title: const Text('البحث'),
        automaticallyImplyLeading: false,
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          children: [
            // Search Input Field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'ابحث عن المنتجات...',
                prefixIcon: Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  child: SvgPicture.asset(
                    AppIcons.search,
                    colorFilter: const ColorFilter.mode(
                      AppColors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                prefixIconConstraints: const BoxConstraints(),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                      )
                    : null,
              ),
              textInputAction: TextInputAction.search,
              onChanged: (value) {
                setState(() {}); // Update UI for clear button
                if (value.trim().isNotEmpty) {
                  _debouncedSearch(value);
                } else {
                  _performSearch(''); // Immediate clear for empty search
                }
              },
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  _navigateToSearchResult(value.trim());
                }
              },
            ),
            const SizedBox(height: 16),

            // Search Results or Loading/Error States
            Expanded(
              child: _buildSearchContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchContent() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'جاري البحث...',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _performSearch(_searchController.text);
              },
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (!_hasSearched) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'ابحث عن المنتجات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'اكتب في حقل البحث للعثور على المنتجات',
              style: TextStyle(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لم يتم العثور على نتائج',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'جرب البحث بكلمات مختلفة',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            'تم العثور على ${_searchResults.length} منتج',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              childAspectRatio: 0.85,
            ),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              final product = _searchResults[index];
              return ProductTileSquare(
                data: product.toProductModel(),
                productId: product.id,
              );
            },
          ),
        ),
      ],
    );
  }

  /// Convert exception to user-friendly Arabic error message
  String _getErrorMessage(dynamic error) {
    if (error is HomeApiException) {
      // Handle specific API errors
      final message = error.message.toLowerCase();

      if (message.contains('timeout') || message.contains('connection')) {
        return 'انتهت مهلة الاتصال. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.';
      } else if (message.contains('network') || message.contains('internet')) {
        return 'لا يوجد اتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
      } else if (message.contains('authentication') ||
          message.contains('401')) {
        return 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.';
      } else if (message.contains('server') || message.contains('500')) {
        return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
      } else if (message.contains('not found') || message.contains('404')) {
        return 'لم يتم العثور على المنتجات المطلوبة.';
      } else {
        return 'حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.';
      }
    } else {
      // Handle generic errors
      return 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }
  }
}

// Details page stub (to be implemented)
class CategoryProductPage extends StatelessWidget {
  final int id;
  final String type; // 'category' or 'company'
  final String name;
  const CategoryProductPage(
      {super.key, required this.id, required this.type, required this.name});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(name)),
      body: Center(child: Text('عرض المنتجات لـ $type: $name (ID: $id)')),
    );
  }
}
