import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/routes/app_routes.dart';
import '../../services/app_services.dart';
import '../../services/cart_service.dart';
import '../../api/wholesaler_api.dart';
import 'components/wholesaler_cart_item_tile.dart';
import 'components/cart_wholesaler_header.dart';
import 'components/cart_min_charge_card.dart';
import 'components/cart_totals_card.dart';

class CartPage extends StatefulWidget {
  const CartPage({
    super.key,
    this.isHomePage = false,
  });

  final bool isHomePage;

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  WholesalerMinChargeResponse? _minChargeInfo;
  bool _isLoadingMinCharge = false;
  String? _minChargeError;

  @override
  void initState() {
    super.initState();
    _loadMinChargeInfo();
  }

  Future<void> _loadMinChargeInfo() async {
    final cartService = AppServices().cartService;
    final regionService = AppServices().regionService;

    if (!cartService.hasCart || regionService.selectedRegion == null) {
      return;
    }

    setState(() {
      _isLoadingMinCharge = true;
      _minChargeError = null;
    });

    try {
      final minCharge = await WholesalerApiService.getWholesalerMinCharge(
        cartService.currentWholesalerId!,
        regionService.selectedRegion!.id,
      );

      if (mounted) {
        setState(() {
          _minChargeInfo = minCharge;
          _isLoadingMinCharge = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _minChargeError = 'حدث خطأ في تحميل الحد الأدنى للطلب';
          _isLoadingMinCharge = false;
        });
      }
    }
  }

  bool get _canProceedToCheckout {
    final cartService = AppServices().cartService;
    if (!cartService.hasCart || _minChargeInfo == null) return false;

    final totalPrice = cartService.totalPrice;
    final uniqueProducts =
        cartService.itemCount; // Number of different products

    return totalPrice >= _minChargeInfo!.minCharge &&
        uniqueProducts >= _minChargeInfo!.minItems;
  }

  void _showMinChargeError() {
    if (_minChargeInfo == null) return;

    final cartService = AppServices().cartService;
    final totalPrice = cartService.totalPrice;
    final uniqueProducts =
        cartService.itemCount; // Number of different products

    String message = '';
    if (totalPrice < _minChargeInfo!.minCharge) {
      final needed = _minChargeInfo!.minCharge - totalPrice;
      message +=
          'تحتاج إلى ${needed.toStringAsFixed(2)} جنيه أخرى للوصول للحد الأدنى';
    }
    if (uniqueProducts < _minChargeInfo!.minItems) {
      final needed = _minChargeInfo!.minItems - uniqueProducts;
      if (message.isNotEmpty) message += '\n';
      message += 'تحتاج إلى $needed منتج أخرى للوصول للحد الأدنى';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isHomePage
          ? null
          : AppBar(
              leading: const AppBackButton(),
              title: const Text(
                'سلة التسوق',
                style: TextStyle(
                  fontFamily: 'Gilroy',
                  fontWeight: FontWeight.bold,
                ),
              ),
              centerTitle: true,
            ),
      body: Consumer<CartService>(
        builder: (context, cartService, child) {
          if (cartService.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (!cartService.hasCart) {
            return _buildEmptyCart();
          }

          return SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Wholesaler Header
                  CartWholesalerHeader(cart: cartService.cart!),

                  const SizedBox(height: AppDefaults.padding),

                  // Cart Items
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: cartService.cart!.items.length,
                    itemBuilder: (context, index) {
                      final item = cartService.cart!.items[index];
                      return WholesalerCartItemTile(
                        cartItem: item,
                        onQuantityChanged: (newQuantity) async {
                          await cartService.updateQuantity(
                            item.wholesalerItemId,
                            newQuantity,
                          );
                        },
                        onRemove: () async {
                          await cartService.removeFromCart(
                            item.wholesalerItemId,
                          );
                          if (cartService.isEmpty) {
                            // Refresh min charge info when cart becomes empty
                            setState(() {
                              _minChargeInfo = null;
                            });
                          }
                        },
                      );
                    },
                  ),

                  const SizedBox(height: AppDefaults.padding),

                  // Minimum Charge Information
                  if (_minChargeInfo != null)
                    CartMinChargeCard(
                      minChargeInfo: _minChargeInfo!,
                      currentTotal: cartService.totalPrice,
                      currentItems:
                          cartService.itemCount, // Number of different products
                    ),

                  if (_isLoadingMinCharge)
                    const Padding(
                      padding: EdgeInsets.all(AppDefaults.padding),
                      child: Center(child: CircularProgressIndicator()),
                    ),

                  if (_minChargeError != null)
                    Padding(
                      padding: const EdgeInsets.all(AppDefaults.padding),
                      child: Container(
                        padding: const EdgeInsets.all(AppDefaults.padding),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius:
                              BorderRadius.circular(AppDefaults.radius),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.error_outline,
                                color: Colors.red.shade600),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _minChargeError!,
                                style: TextStyle(
                                  color: Colors.red.shade700,
                                  fontFamily: 'Gilroy',
                                ),
                              ),
                            ),
                            TextButton(
                              onPressed: _loadMinChargeInfo,
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Coupon Code
                  // const CouponCodeField(),

                  // Cart Totals
                  CartTotalsCard(
                    subtotal: cartService.totalPrice,
                    total: cartService.totalPrice,
                    itemCount: cartService
                        .totalQuantity, // Total quantity for order summary
                  ),

                  // Checkout Button
                  Padding(
                    padding: const EdgeInsets.all(AppDefaults.padding),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _canProceedToCheckout
                            ? () {
                                Navigator.pushNamed(
                                    context, AppRoutes.checkoutPage);
                              }
                            : () {
                                _showMinChargeError();
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _canProceedToCheckout
                              ? Theme.of(context).primaryColor
                              : Colors.grey,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          _canProceedToCheckout
                              ? 'متابعة للدفع'
                              : 'لا يمكن الدفع - لم تصل للحد الأدنى',
                          style: const TextStyle(
                            fontFamily: 'Gilroy',
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 24),
            Text(
              'سلة التسوق فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontFamily: 'Gilroy',
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade600,
                  ),
            ),
            const SizedBox(height: 12),
            Text(
              'أضف منتجات إلى سلة التسوق للمتابعة',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontFamily: 'Gilroy',
                    color: Colors.grey.shade500,
                  ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text(
                'العودة للتسوق',
                style: TextStyle(
                  fontFamily: 'Gilroy',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
