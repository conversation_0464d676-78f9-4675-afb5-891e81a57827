import 'package:flutter/material.dart';
import '../../../api/stores_api.dart';
import '../../../api/regions.dart';
import '../../../models/store_models.dart';
import '../../../services/app_services.dart';

class CreateStoreDialog extends StatefulWidget {
  final int? requiredRegionId;

  const CreateStoreDialog({
    super.key,
    this.requiredRegionId,
  });

  @override
  State<CreateStoreDialog> createState() => _CreateStoreDialogState();
}

class _CreateStoreDialogState extends State<CreateStoreDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();

  bool _isLoading = false;
  String? _error;

  RegionModel? _selectedCountry;
  RegionModel? _selectedState;
  RegionModel? _selectedCity;

  List<RegionModel> _countries = [];
  List<RegionModel> _states = [];
  List<RegionModel> _cities = [];

  @override
  void initState() {
    super.initState();
    _loadCountries();
    _setDefaultRegionFromService();
  }

  void _setDefaultRegionFromService() {
    final regionService = AppServices().regionService;
    final selectedRegion = regionService.selectedRegion;

    if (selectedRegion != null) {
      // Try to set the current region as default
      _setDefaultRegions(selectedRegion);
    }
  }

  void _setDefaultRegions(RegionModel region) {
    // This is a simplified approach - in a real app, you'd need proper region hierarchy
    switch (region.type.toLowerCase()) {
      case 'city':
        _selectedCity = region;
        break;
      case 'state':
        _selectedState = region;
        break;
      case 'country':
        _selectedCountry = region;
        break;
    }
  }

  Future<void> _loadCountries() async {
    try {
      final countries = await RegionsApiService.getCountries();
      setState(() {
        _countries = countries;
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ في تحميل البلدان';
      });
    }
  }

  Future<void> _loadStates(int countryId) async {
    try {
      final allRegions = await RegionsApiService.getAllRegions();
      final states = allRegions
          .where((region) =>
              region.type == 'STATE' && region.parentId == countryId)
          .toList();
      setState(() {
        _states = states;
        _selectedState = null;
        _selectedCity = null;
        _cities = [];
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ في تحميل المحافظات';
      });
    }
  }

  Future<void> _loadCities(int stateId) async {
    try {
      final allRegions = await RegionsApiService.getAllRegions();
      final cities = allRegions
          .where((region) =>
              region.type == 'DISTRICT' && region.parentId == stateId)
          .toList();
      setState(() {
        _cities = cities;
        _selectedCity = null;
      });
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ في تحميل المدن';
      });
    }
  }

  Future<void> _createStore() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCountry == null ||
        _selectedState == null ||
        _selectedCity == null) {
      setState(() {
        _error = 'يجب اختيار البلد والمحافظة والمدينة';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final request = StoreCreateRequest(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        address: _addressController.text.trim(),
        cityId: _selectedCity!.id,
        stateId: _selectedState!.id,
        countryId: _selectedCountry!.id,
      );

      final response = await StoresApiService.createStore(request);
      final store = response.data;

      if (mounted) {
        Navigator.of(context).pop(store);
      }
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ في إنشاء المتجر: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Text(
                  'إضافة عنوان جديد',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontFamily: 'Gilroy',
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Store Name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'اسم المتجر *',
                          hintText: 'مثال: متجر الرئيسي',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم المتجر مطلوب';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'وصف المتجر',
                          hintText: 'وصف قصير للمتجر',
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: 16),

                      // Country Dropdown
                      DropdownButtonFormField<RegionModel>(
                        value: _selectedCountry,
                        decoration: const InputDecoration(
                          labelText: 'البلد *',
                        ),
                        items: _countries.map((country) {
                          return DropdownMenuItem(
                            value: country,
                            child: Text(country.name),
                          );
                        }).toList(),
                        onChanged: (RegionModel? country) {
                          setState(() {
                            _selectedCountry = country;
                          });
                          if (country != null) {
                            _loadStates(country.id);
                          }
                        },
                        validator: (value) {
                          if (value == null) return 'البلد مطلوب';
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // State Dropdown
                      DropdownButtonFormField<RegionModel>(
                        value: _selectedState,
                        decoration: const InputDecoration(
                          labelText: 'المحافظة *',
                        ),
                        items: _states.map((state) {
                          return DropdownMenuItem(
                            value: state,
                            child: Text(state.name),
                          );
                        }).toList(),
                        onChanged: (RegionModel? state) {
                          setState(() {
                            _selectedState = state;
                          });
                          if (state != null) {
                            _loadCities(state.id);
                          }
                        },
                        validator: (value) {
                          if (value == null) return 'المحافظة مطلوبة';
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // City Dropdown
                      DropdownButtonFormField<RegionModel>(
                        value: _selectedCity,
                        decoration: const InputDecoration(
                          labelText: 'المدينة *',
                        ),
                        items: _cities.map((city) {
                          return DropdownMenuItem(
                            value: city,
                            child: Text(city.name),
                          );
                        }).toList(),
                        onChanged: (RegionModel? city) {
                          setState(() {
                            _selectedCity = city;
                          });
                        },
                        validator: (value) {
                          if (value == null) return 'المدينة مطلوبة';
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Address
                      TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: 'العنوان التفصيلي *',
                          hintText: 'رقم المبنى، الشارع، الحي',
                        ),
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'العنوان التفصيلي مطلوب';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 24),

                      // Error Display
                      if (_error != null) ...[
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error_outline,
                                  color: Colors.red.shade600, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  _error!,
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontFamily: 'Gilroy',
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text(
                      'إلغاء',
                      style: TextStyle(fontFamily: 'Gilroy'),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _createStore,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text(
                            'إنشاء المتجر',
                            style: TextStyle(fontFamily: 'Gilroy'),
                          ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    super.dispose();
  }
}
