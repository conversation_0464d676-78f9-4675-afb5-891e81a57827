import 'package:flutter/material.dart';

import '../../../core/constants/constants.dart';

class WholesalerBottomNavigationBar extends StatelessWidget {
  const WholesalerBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onNavTap,
  });

  final int currentIndex;
  final void Function(int) onNavTap;

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: onNavTap,
      backgroundColor: AppColors.scaffoldBackground,
      selectedItemColor: AppColors.primary,
      unselectedItemColor: AppColors.placeholder,
      selectedLabelStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
      ),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.receipt_long),
          label: 'الطلبات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.inventory_2),
          label: 'المنتجات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'الملف الشخصي',
        ),
      ],
    );
  }
}
