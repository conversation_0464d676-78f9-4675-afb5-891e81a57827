import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_defaults.dart';
import '../../../core/components/app_back_button.dart';
import '../../../api/stores_api.dart';
import '../../../api/regions.dart';
import '../../../models/store_models.dart';

class StoreFormPage extends StatefulWidget {
  final StoreData? store;

  const StoreFormPage({super.key, this.store});

  @override
  State<StoreFormPage> createState() => _StoreFormPageState();
}

class _StoreFormPageState extends State<StoreFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();

  List<RegionModel> countries = [];
  List<RegionModel> states = [];
  List<RegionModel> cities = [];

  RegionModel? selectedCountry;
  RegionModel? selectedState;
  RegionModel? selectedCity;

  bool isLoading = false;
  bool isLoadingRegions = true;

  bool get isEditMode => widget.store != null;

  @override
  void initState() {
    super.initState();
    _initializeData();
    if (isEditMode) {
      _loadRegionsForEditMode();
    } else {
      _loadCountries();
    }
  }

  Future<void> _loadRegionsForEditMode() async {
    await _loadCountries();

    selectedCountry =
        countries.firstWhere((c) => c.id == widget.store!.country.id);
    await _loadStates(selectedCountry!.id);
    selectedState = states.firstWhere((s) => s.id == widget.store!.state.id);
    await _loadCities(selectedState!.id);
    selectedCity = cities.firstWhere((c) => c.id == widget.store!.city.id);
  }

  void _initializeData() {
    if (isEditMode) {
      _nameController.text = widget.store!.name;
      _descriptionController.text = widget.store!.description;
      _addressController.text = widget.store!.address;
    }
  }

  Future<void> _loadCountries() async {
    try {
      final response = await RegionsApiService.getCountries();
      if (mounted) {
        setState(() {
          countries = response;
          isLoadingRegions = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoadingRegions = false;
        });
        _showErrorSnackBar('خطأ في تحميل البيانات');
      }
    }
  }

  Future<void> _loadStates(int countryId) async {
    try {
      final response = await RegionsApiService.getStates();
      if (mounted) {
        setState(() {
          states = response.where((r) => r.parentId == countryId).toList();
          cities.clear();
          selectedState = null;
          selectedCity = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل المحافظات');
    }
  }

  Future<void> _loadCities(int stateId) async {
    try {
      final response = await RegionsApiService.getDistricts();
      if (mounted) {
        setState(() {
          cities = response.where((r) => r.parentId == stateId).toList();
          selectedCity = null;
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تحميل المدن');
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedCountry == null ||
        selectedState == null ||
        selectedCity == null) {
      _showErrorSnackBar('يجب اختيار الدولة والمحافظة والمدينة');
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      ApiResponse<StoreData> response;

      if (isEditMode) {
        final request = StoreUpdateRequest(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          address: _addressController.text.trim(),
          cityId: selectedCity!.id,
          stateId: selectedState!.id,
          countryId: selectedCountry!.id,
        );
        response =
            await StoresApiService.updateStore(widget.store!.id, request);
      } else {
        final request = StoreCreateRequest(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          address: _addressController.text.trim(),
          cityId: selectedCity!.id,
          stateId: selectedState!.id,
          countryId: selectedCountry!.id,
        );
        response = await StoresApiService.createStore(request);
      }

      if (mounted) {
        setState(() {
          isLoading = false;
        });

        if (response.success) {
          _showSuccessSnackBar(response.message ??
              (isEditMode ? 'تم تحديث المتجر بنجاح' : 'تم إنشاء المتجر بنجاح'));
          Navigator.of(context).pop();
        } else {
          _showErrorSnackBar(response.message ?? 'حدث خطأ');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('خطأ في الاتصال. يرجى المحاولة مرة أخرى');
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.scaffoldBackground,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: Text(isEditMode ? 'تعديل المتجر' : 'إضافة متجر جديد'),
        backgroundColor: AppColors.scaffoldBackground,
        elevation: 0,
      ),
      body: isLoadingRegions
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Container(
                margin: const EdgeInsets.all(AppDefaults.padding),
                padding: const EdgeInsets.all(AppDefaults.padding),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: AppDefaults.borderRadius,
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTextField(
                        label: 'اسم المتجر',
                        controller: _nameController,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم المتجر مطلوب';
                          }
                          if (value.trim().length < 2) {
                            return 'اسم المتجر يجب أن يكون حرفين على الأقل';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDefaults.padding),
                      _buildTextField(
                        label: 'وصف المتجر',
                        controller: _descriptionController,
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'وصف المتجر مطلوب';
                          }
                          if (value.trim().length < 10) {
                            return 'وصف المتجر يجب أن يكون 10 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDefaults.padding),
                      _buildTextField(
                        label: 'عنوان المتجر',
                        controller: _addressController,
                        maxLines: 2,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'عنوان المتجر مطلوب';
                          }
                          if (value.trim().length < 10) {
                            return 'عنوان المتجر يجب أن يكون 10 أحرف على الأقل';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: AppDefaults.padding),
                      _buildDropdown(
                        label: 'الدولة',
                        value: selectedCountry,
                        items: countries,
                        onChanged: (RegionModel? country) {
                          setState(() {
                            selectedCountry = country;
                            selectedState = null;
                            selectedCity = null;
                            states.clear();
                            cities.clear();
                          });
                          if (country != null) {
                            _loadStates(country.id);
                          }
                        },
                      ),
                      const SizedBox(height: AppDefaults.padding),
                      _buildDropdown(
                        label: 'المحافظة',
                        value: selectedState,
                        items: states,
                        onChanged: (RegionModel? state) {
                          setState(() {
                            selectedState = state;
                            selectedCity = null;
                            cities.clear();
                          });
                          if (state != null) {
                            _loadCities(state.id);
                          }
                        },
                        enabled: selectedCountry != null,
                      ),
                      const SizedBox(height: AppDefaults.padding),
                      _buildDropdown(
                        label: 'المدينة',
                        value: selectedCity,
                        items: cities,
                        onChanged: (RegionModel? city) {
                          setState(() {
                            selectedCity = city;
                          });
                        },
                        enabled: selectedState != null,
                      ),
                      const SizedBox(height: AppDefaults.padding * 2),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: isLoading ? null : _submitForm,
                          child: isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : Text(
                                  isEditMode ? 'تحديث المتجر' : 'إنشاء المتجر'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdown({
    required String label,
    required RegionModel? value,
    required List<RegionModel> items,
    required ValueChanged<RegionModel?> onChanged,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<RegionModel>(
          value: value,
          items: items.map((region) {
            return DropdownMenuItem<RegionModel>(
              value: region,
              child: Text(region.name),
            );
          }).toList(),
          onChanged: enabled ? onChanged : null,
          decoration: InputDecoration(
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
            hintText: enabled ? 'اختر $label' : 'اختر الدولة أولاً',
          ),
          validator: (value) {
            if (value == null) {
              return 'يجب اختيار $label';
            }
            return null;
          },
        ),
      ],
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    super.dispose();
  }
}
