import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_defaults.dart';
import '../../../core/components/app_back_button.dart';
import '../../../api/stores_api.dart';
import '../../../models/store_models.dart';
import '../../../core/routes/app_routes.dart';
import '../components/store_tile.dart';

class StoresListPage extends StatefulWidget {
  const StoresListPage({super.key});

  @override
  State<StoresListPage> createState() => _StoresListPageState();
}

class _StoresListPageState extends State<StoresListPage> {
  List<StoreData> stores = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadStores();
  }

  Future<void> _loadStores() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    final response = await StoresApiService.getUserStores();

    if (mounted) {
      setState(() {
        isLoading = false;
        if (response.success) {
          stores = response.data ?? [];
        } else {
          errorMessage = response.message;
        }
      });
    }
  }

  Future<void> _deleteStore(StoreData store) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المتجر'),
        content: Text('هل أنت متأكد من حذف متجر "${store.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final response = await StoresApiService.deleteStore(store.id);

      if (mounted) {
        if (response.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? 'تم حذف المتجر بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadStores(); // Refresh the list
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.message ?? 'حدث خطأ في حذف المتجر'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _editStore(StoreData store) {
    Navigator.pushNamed(
      context,
      AppRoutes.editStore,
      arguments: store,
    ).then((_) => _loadStores()); // Refresh after edit
  }

  void _addNewStore() {
    Navigator.pushNamed(
      context,
      AppRoutes.createStore,
    ).then((_) => _loadStores()); // Refresh after creation
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text('إدارة المتاجر'),
        backgroundColor: AppColors.cardColor,
        elevation: 0,
      ),
      body: Container(
        margin: const EdgeInsets.all(AppDefaults.margin),
        padding: const EdgeInsets.all(AppDefaults.padding),
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
          borderRadius: AppDefaults.borderRadius,
        ),
        child: Stack(
          children: [
            if (isLoading) const Center(child: CircularProgressIndicator()),
            if (!isLoading && errorMessage != null)
              Center(
                child: Text(
                  errorMessage!,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(color: Colors.red),
                ),
              ),
            if (!isLoading && errorMessage == null)
              ListView.separated(
                itemCount: stores.length,
                separatorBuilder: (context, index) =>
                    const Divider(thickness: 0.2),
                itemBuilder: (context, index) {
                  final store = stores[index];
                  return StoreTile(
                    name: store.name,
                    address: store.fullAddress,
                    phone: store.owner.phone,
                    isActive: index == 0, // Mark first as active for demo
                    onEdit: () => _editStore(store),
                    onDelete: () => _deleteStore(store),
                  );
                },
              ),
            Positioned(
              bottom: 16,
              right: 16,
              child: FloatingActionButton(
                onPressed: _addNewStore,
                backgroundColor: AppColors.primary,
                splashColor: AppColors.primary,
                child: const Icon(Icons.add),
                tooltip: 'إضافة متجر جديد',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
