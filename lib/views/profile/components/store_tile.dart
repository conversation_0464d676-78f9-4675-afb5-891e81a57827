import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../../core/components/app_radio.dart';
import '../../../core/constants/constants.dart';

class StoreTile extends StatelessWidget {
  const StoreTile({
    super.key,
    required this.name,
    required this.address,
    required this.phone,
    required this.isActive,
    required this.onEdit,
    required this.onDelete,
    this.onTap,
  });

  final String name;
  final String address;
  final String phone;
  final bool isActive;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppRadio(isActive: isActive),
            const SizedBox(width: AppDefaults.padding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.black,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(address),
                  const SizedBox(height: 4),
                  Text(
                    phone,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.black,
                        ),
                  )
                ],
              ),
            ),
            const SizedBox(width: 8),
            Column(
              children: [
                IconButton(
                  onPressed: onEdit,
                  icon: SvgPicture.asset(AppIcons.edit),
                  constraints: const BoxConstraints(),
                  iconSize: 14,
                ),
                const SizedBox(height: AppDefaults.margin / 2),
                // IconButton(
                //   onPressed: onDelete,
                //   icon: SvgPicture.asset(AppIcons.deleteOutline),
                //   constraints: const BoxConstraints(),
                //   iconSize: 14,
                // ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
