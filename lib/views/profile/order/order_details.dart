import 'package:flutter/material.dart';

import '../../../core/components/app_back_button.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_defaults.dart';
import '../../../api/orders_api.dart';
import 'components/order_details_statuses.dart';
import 'components/order_details_total_amount_and_paid.dart';
import 'components/order_details_total_order_product_details.dart';

class OrderDetailsPage extends StatefulWidget {
  const OrderDetailsPage({
    super.key,
    required this.orderId,
    required this.storeId,
  });

  final int orderId;
  final int storeId;

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  OrderResponse? order;
  bool isLoading = false;
  String error = '';

  @override
  void initState() {
    super.initState();
    loadOrderDetails();
  }

  Future<void> loadOrderDetails() async {
    try {
      setState(() {
        isLoading = true;
        error = '';
      });

      final orderData = await OrdersApiService.getOrderById(
        storeId: widget.storeId,
        orderId: widget.orderId,
      );

      setState(() {
        order = orderData;
      });
    } catch (e) {
      setState(() {
        error = 'فشل في تحميل تفاصيل الطلب: ${e.toString()}';
      });
      print('Error loading order details: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> refreshOrder() async {
    await loadOrderDetails();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text('تفاصيل الطلب'),
        actions: [
          if (order != null)
            IconButton(
              onPressed: refreshOrder,
              icon: const Icon(Icons.refresh),
            ),
        ],
      ),
      body: Builder(
        builder: (context) {
          if (isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (error.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade300,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    error,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: refreshOrder,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (order == null) {
            return const Center(
              child: Text('لم يتم العثور على الطلب'),
            );
          }

          return SingleChildScrollView(
            child: Container(
              margin: const EdgeInsets.all(AppDefaults.margin),
              padding: const EdgeInsets.all(AppDefaults.padding),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: AppDefaults.borderRadius,
              ),
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'رقم الطلب #${order!.id}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold, color: Colors.black),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'المتجر: ${order!.storeName}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.placeholder,
                          ),
                    ),
                  ),
                  const SizedBox(height: AppDefaults.padding),
                  OrderStatusColumn(order: order!),
                  TotalOrderProductDetails(order: order!),
                  TotalAmountAndPaidData(order: order!),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
