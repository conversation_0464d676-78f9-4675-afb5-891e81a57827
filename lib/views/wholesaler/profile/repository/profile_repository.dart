import 'package:flutter/foundation.dart';
import '../api/profile_api_service.dart';
import '../models/profile_models.dart';

/// Repository for managing wholesaler profile data and state
class ProfileRepository extends ChangeNotifier {
  // Profile data
  WholesalerProfileData? _profileData;

  // Loading states
  bool _isLoadingProfile = false;
  bool _isUpdatingProfile = false;
  bool _isUploadingImage = false;

  // Error states
  String? _profileError;
  String? _updateError;
  String? _uploadError;

  // Getters
  WholesalerProfileData? get profileData => _profileData;
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isUpdatingProfile => _isUpdatingProfile;
  bool get isUploadingImage => _isUploadingImage;
  String? get profileError => _profileError;
  String? get updateError => _updateError;
  String? get uploadError => _uploadError;

  bool get hasData => _profileData != null;
  bool get hasError =>
      _profileError != null || _updateError != null || _uploadError != null;
  bool get isLoading =>
      _isLoadingProfile || _isUpdatingProfile || _isUploadingImage;

  /// Load profile data
  Future<void> loadProfile({bool forceRefresh = false}) async {
    if (_profileData != null && !forceRefresh) return;

    _isLoadingProfile = true;
    _profileError = null;
    notifyListeners();

    try {
      _profileData = await ProfileApiService.loadProfileData();
      _profileError = null;
    } catch (e) {
      if (kDebugMode) rethrow;
      _profileError = e.toString();
    } finally {
      _isLoadingProfile = false;
      notifyListeners();
    }
  }

  /// Update profile data
  Future<bool> updateProfile(ProfileUpdateRequest request) async {
    if (request.isEmpty) {
      _updateError = 'لا توجد تغييرات للحفظ';
      notifyListeners();
      return false;
    }

    _isUpdatingProfile = true;
    _updateError = null;
    notifyListeners();

    try {
      _profileData = await ProfileApiService.updateProfile(request);
      _updateError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _updateError = e.toString();
      notifyListeners();
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }

  /// Upload profile image
  Future<bool> uploadProfileImage(String filePath) async {
    _isUploadingImage = true;
    _uploadError = null;
    notifyListeners();

    try {
      _profileData = await ProfileApiService.uploadProfileImage(filePath);
      _uploadError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _uploadError = e.toString();
      notifyListeners();
      return false;
    } finally {
      _isUploadingImage = false;
      notifyListeners();
    }
  }

  /// Upload background image
  Future<bool> uploadBackgroundImage(String filePath) async {
    _isUploadingImage = true;
    _uploadError = null;
    notifyListeners();

    try {
      _profileData = await ProfileApiService.uploadBackgroundImage(filePath);
      _uploadError = null;
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) rethrow;
      _uploadError = e.toString();
      notifyListeners();
      return false;
    } finally {
      _isUploadingImage = false;
      notifyListeners();
    }
  }

  /// Refresh all data
  Future<void> refreshAll() async {
    await loadProfile(forceRefresh: true);
  }

  /// Clear all errors
  void clearErrors() {
    _profileError = null;
    _updateError = null;
    _uploadError = null;
    notifyListeners();
  }

  /// Clear all data (useful for logout)
  void clearData() {
    _profileData = null;
    _profileError = null;
    _updateError = null;
    _uploadError = null;
    _isLoadingProfile = false;
    _isUpdatingProfile = false;
    _isUploadingImage = false;
    notifyListeners();
  }

  /// Get user display name
  String getDisplayName() {
    return _profileData?.userData.fullName ?? 'المستخدم';
  }

  /// Get store display name
  String getStoreName() {
    return _profileData?.wholesalerData.title ?? 'المتجر';
  }

  /// Check if phone is verified
  bool isPhoneVerified() {
    return _profileData?.userData.phoneVerified ?? false;
  }

  /// Get store category display name
  String getCategoryDisplayName() {
    if (_profileData?.wholesalerData.category == null) return 'غير محدد';
    return StoreCategories.getDisplayName(
        _profileData!.wholesalerData.category!);
  }

  /// Get profile image URL
  String? getProfileImageUrl() {
    return _profileData?.wholesalerData.logoUrl;
  }

  /// Get background image URL
  String? getBackgroundImageUrl() {
    return _profileData?.wholesalerData.backgroundImageUrl;
  }

  /// Get store creation date
  DateTime? getStoreCreationDate() {
    // Note: WholesalerProfile doesn't have createdAt field
    return null;
  }

  /// Get user join date
  DateTime? getUserJoinDate() {
    return _profileData?.userData.dateJoined;
  }
}
