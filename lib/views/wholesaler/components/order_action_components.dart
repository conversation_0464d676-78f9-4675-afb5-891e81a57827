import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../models/wholesaler_models.dart';
import '../repository/wholesaler_orders_repository.dart';

/// Reusable order action button component
class OrderActionButton extends StatefulWidget {
  final String label;
  final IconData icon;
  final Color backgroundColor;
  final Color foregroundColor;
  final VoidCallback onPressed;
  final bool isLoading;
  final bool isOutlined;

  const OrderActionButton({
    super.key,
    required this.label,
    required this.icon,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
  });

  @override
  State<OrderActionButton> createState() => _OrderActionButtonState();
}

class _OrderActionButtonState extends State<OrderActionButton> {
  @override
  Widget build(BuildContext context) {
    if (widget.isOutlined) {
      return SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: widget.isLoading ? null : widget.onPressed,
          icon: widget.isLoading
              ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(widget.foregroundColor),
                  ),
                )
              : Icon(widget.icon),
          label: Text(widget.label),
          style: OutlinedButton.styleFrom(
            foregroundColor: widget.foregroundColor,
            side: BorderSide(color: widget.backgroundColor),
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: widget.isLoading ? null : widget.onPressed,
        icon: widget.isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Icon(widget.icon),
        label: Text(widget.label),
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.foregroundColor,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
      ),
    );
  }
}

/// Order actions widget that handles all order actions
class OrderActionsWidget extends StatefulWidget {
  final WholesalerOrder order;
  final WholesalerOrdersRepository repository;
  final VoidCallback? onActionCompleted;

  const OrderActionsWidget({
    super.key,
    required this.order,
    required this.repository,
    this.onActionCompleted,
  });

  @override
  State<OrderActionsWidget> createState() => _OrderActionsWidgetState();
}

class _OrderActionsWidgetState extends State<OrderActionsWidget> {
  bool _isAcceptLoading = false;
  bool _isRejectLoading = false;
  bool _isCancelLoading = false;
  bool _isCompleteLoading = false;

  Future<void> _acceptOrder() async {
    final confirmed = await _showConfirmationDialog(
      title: 'قبول الطلب',
      message: 'هل أنت متأكد من قبول هذا الطلب؟',
      confirmText: 'قبول',
      confirmColor: Colors.green,
    );

    if (confirmed == true) {
      setState(() => _isAcceptLoading = true);

      final success = await widget.repository.acceptOrder(widget.order.id);

      setState(() => _isAcceptLoading = false);

      if (success) {
        _showSuccessSnackBar('تم قبول الطلب بنجاح');
        widget.onActionCompleted?.call();
      } else {
        _showErrorSnackBar(widget.repository.error ?? 'فشل في قبول الطلب');
      }
    }
  }

  Future<void> _rejectOrder() async {
    final confirmed = await _showConfirmationDialog(
      title: 'رفض الطلب',
      message:
          'هل أنت متأكد من رفض هذا الطلب؟\nسيتم خصم رسوم قدرها ${widget.order.fees.toStringAsFixed(2)} ج.م من محفظتك.',
      confirmText: 'رفض',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      setState(() => _isRejectLoading = true);

      final success = await widget.repository.rejectOrder(widget.order.id);

      setState(() => _isRejectLoading = false);

      if (success) {
        _showSuccessSnackBar('تم رفض الطلب');
        widget.onActionCompleted?.call();
      } else {
        _showErrorSnackBar(widget.repository.error ?? 'فشل في رفض الطلب');
      }
    }
  }

  Future<void> _cancelOrder() async {
    final confirmed = await _showConfirmationDialog(
      title: 'إلغاء الطلب',
      message:
          'هل أنت متأكد من إلغاء هذا الطلب؟\nسيتم خصم رسوم قدرها ${widget.order.fees.toStringAsFixed(2)} ج.م من محفظتك.',
      confirmText: 'إلغاء',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      setState(() => _isCancelLoading = true);

      final success = await widget.repository.cancelOrder(widget.order.id);

      setState(() => _isCancelLoading = false);

      if (success) {
        _showSuccessSnackBar('تم إلغاء الطلب');
        widget.onActionCompleted?.call();
      } else {
        _showErrorSnackBar(widget.repository.error ?? 'فشل في إلغاء الطلب');
      }
    }
  }

  Future<void> _completeOrder() async {
    final finalPrice = await _showCompletePriceDialog();
    if (finalPrice != null) {
      setState(() => _isCompleteLoading = true);

      final success = await widget.repository.completeOrder(
        widget.order.id,
        finalPrice: finalPrice,
      );

      setState(() => _isCompleteLoading = false);

      if (success) {
        _showSuccessSnackBar('تم إكمال الطلب بنجاح');
        widget.onActionCompleted?.call();
      } else {
        _showErrorSnackBar(widget.repository.error ?? 'فشل في إكمال الطلب');
      }
    }
  }

  Future<bool?> _showConfirmationDialog({
    required String title,
    required String message,
    required String confirmText,
    required Color confirmColor,
  }) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  Future<double?> _showCompletePriceDialog() async {
    final controller = TextEditingController(
      text: widget.order.totalPrice.toStringAsFixed(2),
    );

    return showDialog<double>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: const Text(
          'إكمال الطلب',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('أدخل السعر النهائي للطلب:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                labelText: 'السعر النهائي',
                suffixText: 'ج.م',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'السعر الأصلي: ${widget.order.totalPrice.toStringAsFixed(2)} ج.م',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final price = double.tryParse(controller.text);
              if (price != null && price > 0) {
                Navigator.of(context).pop(price);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال سعر صحيح'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('إكمال'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات الطلب',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        if (widget.order.canAccept) ...[
          OrderActionButton(
            label: 'قبول الطلب',
            icon: Icons.check,
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            onPressed: _acceptOrder,
            isLoading: _isAcceptLoading,
          ),
          const SizedBox(height: 8),
        ],
        if (widget.order.canReject) ...[
          OrderActionButton(
            label: 'رفض الطلب',
            icon: Icons.close,
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            onPressed: _rejectOrder,
            isLoading: _isRejectLoading,
          ),
          const SizedBox(height: 8),
        ],
        if (widget.order.canCancel) ...[
          OrderActionButton(
            label: 'إلغاء الطلب',
            icon: Icons.cancel,
            backgroundColor: Colors.red,
            foregroundColor: Colors.red,
            onPressed: _cancelOrder,
            isLoading: _isCancelLoading,
            isOutlined: true,
          ),
          const SizedBox(height: 8),
        ],
        if (widget.order.canComplete) ...[
          OrderActionButton(
            label: 'إكمال الطلب',
            icon: Icons.check_circle,
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            onPressed: _completeOrder,
            isLoading: _isCompleteLoading,
          ),
        ],
        if (!widget.order.canAccept &&
            !widget.order.canReject &&
            !widget.order.canCancel &&
            !widget.order.canComplete) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'لا توجد إجراءات متاحة لهذا الطلب',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ),
        ],
      ],
    );
  }
}
