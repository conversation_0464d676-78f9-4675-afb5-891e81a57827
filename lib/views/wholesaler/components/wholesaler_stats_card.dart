import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';

class WholesalerStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? iconColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final bool isLoading;

  const WholesalerStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.iconColor,
    this.backgroundColor,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 2,
      borderRadius: AppDefaults.borderRadius,
      child: InkWell(
        onTap: onTap,
        borderRadius: AppDefaults.borderRadius,
        child: Container(
          padding: const EdgeInsets.all(AppDefaults.padding),
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white,
            borderRadius: AppDefaults.borderRadius,
            border: Border.all(color: Colors.grey[300]!),
          ),
          child:
              isLoading ? _buildLoadingState(context) : _buildContent(context),
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 60,
          height: 24,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: 80,
          height: 16,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 32,
          color: iconColor ?? AppColors.primary,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
        ),
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
      ],
    );
  }
}

class WholesalerStatsGrid extends StatelessWidget {
  final int totalProducts;
  final int totalOrders;
  final double totalRevenue;
  final int totalCustomers;
  final int lowStockItems;
  final int expiredItems;
  final bool isLoading;
  final VoidCallback? onProductsTap;
  final VoidCallback? onOrdersTap;
  final VoidCallback? onRevenueTap;
  final VoidCallback? onCustomersTap;
  final VoidCallback? onLowStockTap;
  final VoidCallback? onExpiredTap;

  const WholesalerStatsGrid({
    super.key,
    required this.totalProducts,
    required this.totalOrders,
    required this.totalRevenue,
    required this.totalCustomers,
    required this.lowStockItems,
    required this.expiredItems,
    this.isLoading = false,
    this.onProductsTap,
    this.onOrdersTap,
    this.onRevenueTap,
    this.onCustomersTap,
    this.onLowStockTap,
    this.onExpiredTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: AppDefaults.margin),

        // First row - Main stats
        Row(
          children: [
            Expanded(
              child: WholesalerStatsCard(
                title: 'المنتجات',
                value: totalProducts.toString(),
                icon: Icons.inventory,
                onTap: onProductsTap,
                isLoading: isLoading,
              ),
            ),
            const SizedBox(width: AppDefaults.margin),
            Expanded(
              child: WholesalerStatsCard(
                title: 'الطلبات',
                value: totalOrders.toString(),
                icon: Icons.shopping_cart,
                onTap: onOrdersTap,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDefaults.margin),

        // Second row - Revenue and customers
        Row(
          children: [
            Expanded(
              child: WholesalerStatsCard(
                title: 'الإيرادات',
                value: '${totalRevenue.toStringAsFixed(2)} ج.م',
                icon: Icons.attach_money,
                iconColor: Colors.green,
                onTap: onRevenueTap,
                isLoading: isLoading,
              ),
            ),
            const SizedBox(width: AppDefaults.margin),
            Expanded(
              child: WholesalerStatsCard(
                title: 'العملاء',
                value: totalCustomers.toString(),
                icon: Icons.people,
                iconColor: Colors.blue,
                onTap: onCustomersTap,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDefaults.margin),

        // Third row - Alerts
        Row(
          children: [
            Expanded(
              child: WholesalerStatsCard(
                title: 'مخزون منخفض',
                value: lowStockItems.toString(),
                icon: Icons.warning,
                iconColor: lowStockItems > 0 ? Colors.orange : Colors.grey,
                backgroundColor: lowStockItems > 0 ? Colors.orange[50] : null,
                onTap: onLowStockTap,
                isLoading: isLoading,
              ),
            ),
            const SizedBox(width: AppDefaults.margin),
            Expanded(
              child: WholesalerStatsCard(
                title: 'منتهية الصلاحية',
                value: expiredItems.toString(),
                icon: Icons.schedule,
                iconColor: expiredItems > 0 ? Colors.red : Colors.grey,
                backgroundColor: expiredItems > 0 ? Colors.red[50] : null,
                onTap: onExpiredTap,
                isLoading: isLoading,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
