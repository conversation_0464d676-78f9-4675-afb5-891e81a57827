import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../models/wholesaler_models.dart';

class WholesalerItemCard extends StatelessWidget {
  final WholesalerItem item;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onExpire;
  final VoidCallback? onInventoryUpdate;

  const WholesalerItemCard({
    super.key,
    required this.item,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onExpire,
    this.onInventoryUpdate,
  });

  @override
  Widget build(BuildContext context) {
    final isExpired =
        item.priceExpiry != null && item.priceExpiry!.isBefore(DateTime.now());
    final isLowStock = item.inventoryCount < 10;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(
        horizontal: AppDefaults.padding,
        vertical: AppDefaults.margin / 2,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: AppDefaults.borderRadius,
        child: Padding(
          padding: const EdgeInsets.all(AppDefaults.padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: AppDefaults.borderRadius,
                      color: Colors.grey[200],
                    ),
                    child: item.product.imageUrl != null
                        ? ClipRRect(
                            borderRadius: AppDefaults.borderRadius,
                            child: Image.network(
                              item.product.imageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildPlaceholderImage(),
                            ),
                          )
                        : _buildPlaceholderImage(),
                  ),
                  const SizedBox(width: AppDefaults.margin),

                  // Product Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.product.name,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (item.product.barcode != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            'الباركود: ${item.product.barcode}',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                          ),
                        ],
                        const SizedBox(height: 8),

                        // Price and Inventory
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '${item.basePrice.toStringAsFixed(2)} ج.م',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall
                                    ?.copyWith(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: isLowStock
                                    ? Colors.orange[100]
                                    : Colors.green[100],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'المخزون: ${item.inventoryCount}',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall
                                    ?.copyWith(
                                      color: isLowStock
                                          ? Colors.orange[800]
                                          : Colors.green[800],
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Actions Menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          onEdit?.call();
                          break;
                        case 'inventory':
                          onInventoryUpdate?.call();
                          break;
                        case 'expire':
                          onExpire?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 16),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'inventory',
                        child: Row(
                          children: [
                            Icon(Icons.warehouse, size: 16),
                            SizedBox(width: 8),
                            Text('تحديث المخزون'),
                          ],
                        ),
                      ),
                      if (!isExpired)
                        const PopupMenuItem(
                          value: 'expire',
                          child: Row(
                            children: [
                              Icon(Icons.schedule, size: 16),
                              SizedBox(width: 8),
                              Text('انتهاء الصلاحية'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // Status Indicators
              if (isExpired || isLowStock) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: [
                    if (isExpired)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.schedule,
                                size: 12, color: Colors.red[800]),
                            const SizedBox(width: 4),
                            Text(
                              'منتهية الصلاحية',
                              style: TextStyle(
                                color: Colors.red[800],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (isLowStock)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange[100],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.warning,
                                size: 12, color: Colors.orange[800]),
                            const SizedBox(width: 4),
                            Text(
                              'مخزون منخفض',
                              style: TextStyle(
                                color: Colors.orange[800],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],

              // Expiry Date
              if (item.priceExpiry != null) ...[
                const SizedBox(height: 8),
                Text(
                  'تنتهي في: ${_formatDate(item.priceExpiry!)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isExpired ? Colors.red : Colors.grey[600],
                      ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: AppDefaults.borderRadius,
      ),
      child: Icon(
        Icons.inventory,
        color: Colors.grey[400],
        size: 24,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class WholesalerItemList extends StatelessWidget {
  final List<WholesalerItem> items;
  final bool isLoading;
  final String? error;
  final VoidCallback? onRefresh;
  final Function(WholesalerItem)? onItemTap;
  final Function(WholesalerItem)? onItemEdit;
  final Function(WholesalerItem)? onItemDelete;
  final Function(WholesalerItem)? onItemExpire;
  final Function(WholesalerItem)? onInventoryUpdate;

  const WholesalerItemList({
    super.key,
    required this.items,
    this.isLoading = false,
    this.error,
    this.onRefresh,
    this.onItemTap,
    this.onItemEdit,
    this.onItemDelete,
    this.onItemExpire,
    this.onInventoryUpdate,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && items.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل المنتجات',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (onRefresh != null)
              ElevatedButton(
                onPressed: onRefresh,
                child: const Text('إعادة المحاولة'),
              ),
          ],
        ),
      );
    }

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد منتجات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'أضف منتجك الأول للبدء',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        onRefresh?.call();
      },
      child: ListView.builder(
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return WholesalerItemCard(
            item: item,
            onTap: () => onItemTap?.call(item),
            onEdit: () => onItemEdit?.call(item),
            onDelete: () => onItemDelete?.call(item),
            onExpire: () => onItemExpire?.call(item),
            onInventoryUpdate: () => onInventoryUpdate?.call(item),
          );
        },
      ),
    );
  }
}
