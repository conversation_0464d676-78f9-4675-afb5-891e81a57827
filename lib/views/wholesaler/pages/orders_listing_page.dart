import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../api/orders_api.dart';
import '../repository/wholesaler_orders_repository.dart';
import '../models/wholesaler_models.dart';
import '../components/wholesaler_action_button.dart';

class WholesalerOrdersListingPage extends StatefulWidget {
  const WholesalerOrdersListingPage({super.key});

  @override
  State<WholesalerOrdersListingPage> createState() =>
      _WholesalerOrdersListingPageState();
}

class _WholesalerOrdersListingPageState
    extends State<WholesalerOrdersListingPage> {
  late WholesalerOrdersRepository _repository;
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _repository = WholesalerOrdersRepository();
    _loadOrders();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _repository.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _repository.loadMoreOrders();
      }
    });
  }

  Future<void> _loadOrders() async {
    await _repository.loadOrders(forceRefresh: true);
  }

  void _onSearchChanged(String query) {
    _repository.setSearchQuery(query);
  }

  void _onStatusFilterChanged(OrderStatus? status) {
    _repository.setStatusFilter(status);
  }

  void _navigateToOrderDetails(WholesalerOrder order) {
    Navigator.pushNamed(
      context,
      AppRoutes.wholesalerOrderDetails,
      arguments: {'orderId': order.id},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: AppBar(
        title: const Text('إدارة الطلبات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: _loadOrders,
        child: ListView(
          children: [
            // Search and Filter Section
            Container(
              color: Colors.white,
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    decoration: InputDecoration(
                      hintText: 'البحث في الطلبات...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: AppColors.primary),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                  ),
                  const SizedBox(height: AppDefaults.padding),

                  // Status Filter Chips
                  AnimatedBuilder(
                    animation: _repository,
                    builder: (context, child) {
                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            WholesalerActionChip(
                              label: 'الكل',
                              isSelected: _repository.statusFilter == null,
                              onPressed: () => _onStatusFilterChanged(null),
                            ),
                            const SizedBox(width: 8),
                            WholesalerActionChip(
                              label:
                                  'قيد الانتظار (${_repository.pendingOrdersCount})',
                              icon: Icons.pending,
                              isSelected: _repository.statusFilter ==
                                  OrderStatus.pending,
                              backgroundColor: Colors.blue[50],
                              foregroundColor: Colors.blue[800],
                              onPressed: () =>
                                  _onStatusFilterChanged(OrderStatus.pending),
                            ),
                            const SizedBox(width: 8),
                            WholesalerActionChip(
                              label:
                                  'قيد التحضير (${_repository.processingOrdersCount})',
                              icon: Icons.hourglass_empty,
                              isSelected: _repository.statusFilter ==
                                  OrderStatus.processing,
                              backgroundColor: Colors.orange[50],
                              foregroundColor: Colors.orange[800],
                              onPressed: () => _onStatusFilterChanged(
                                  OrderStatus.processing),
                            ),
                            const SizedBox(width: 8),
                            WholesalerActionChip(
                              label:
                                  'مكتملة (${_repository.deliveredOrdersCount})',
                              icon: Icons.check_circle,
                              isSelected: _repository.statusFilter ==
                                  OrderStatus.delivered,
                              backgroundColor: Colors.green[50],
                              foregroundColor: Colors.green[800],
                              onPressed: () =>
                                  _onStatusFilterChanged(OrderStatus.delivered),
                            ),
                            const SizedBox(width: 8),
                            WholesalerActionChip(
                              label:
                                  'ملغية (${_repository.cancelledOrdersCount})',
                              icon: Icons.cancel,
                              isSelected: _repository.statusFilter ==
                                  OrderStatus.cancelled,
                              backgroundColor: Colors.red[50],
                              foregroundColor: Colors.red[800],
                              onPressed: () =>
                                  _onStatusFilterChanged(OrderStatus.cancelled),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // Orders List
            AnimatedBuilder(
              animation: _repository,
              builder: (context, child) {
                if (_repository.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                if (_repository.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _repository.error!,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadOrders,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                final orders = _repository.filteredOrders;

                if (orders.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.receipt_long_outlined,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد طلبات',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'لم يتم العثور على أي طلبات',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey.shade500,
                                  ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  controller: _scrollController,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  itemCount:
                      orders.length + (_repository.isLoadingMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index >= orders.length) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }

                    final order = orders[index];
                    return _OrderCard(
                      order: order,
                      onTap: () => _navigateToOrderDetails(order),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _OrderCard extends StatelessWidget {
  final WholesalerOrder order;
  final VoidCallback onTap;

  const _OrderCard({
    required this.order,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDefaults.margin),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(AppDefaults.padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'طلب #${order.id}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: order.statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: order.statusColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      order.status.displayName,
                      style: TextStyle(
                        color: order.statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Store Info
              Row(
                children: [
                  Icon(
                    Icons.store,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      order.store.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              // Customer Info
              Row(
                children: [
                  Icon(
                    Icons.person,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    order.store.owner.fullName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Order Details
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المبلغ الإجمالي',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      Text(
                        '${order.totalPrice.toStringAsFixed(2)} ج.م',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'عدد المنتجات',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      Text(
                        '${order.productsTotalQuantity}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Delivery Date
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'موعد التسليم: ${order.formattedDeliveryDate}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
