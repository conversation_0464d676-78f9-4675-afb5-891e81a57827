import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../../../core/constants/constants.dart';
import '../models/wholesaler_models.dart' as models;
import '../api/wholesaler_api_service.dart';

class ProductSelectionPage extends StatefulWidget {
  final int? selectedCompanyId;

  const ProductSelectionPage({
    super.key,
    this.selectedCompanyId,
  });

  @override
  State<ProductSelectionPage> createState() => _ProductSelectionPageState();
}

class _ProductSelectionPageState extends State<ProductSelectionPage> {
  final TextEditingController _searchController = TextEditingController();
  final List<models.Product> _products = [];
  final List<models.Product> _filteredProducts = [];
  final List<models.Company> _companies = [];

  bool _isLoading = false;
  String? _error;
  Timer? _debounceTimer;
  models.Company? _selectedCompany;
  int _currentPage = 1;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onSearchChanged() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _loadProducts(reset: true);
    });
  }

  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load companies for filtering
      final companies = await WholesalerApiService.getCompanies();

      // Set selected company if provided
      if (widget.selectedCompanyId != null) {
        _selectedCompany = companies.firstWhere(
          (company) => company.id == widget.selectedCompanyId,
          orElse: () => companies.first,
        );
      }

      setState(() {
        _companies.clear();
        _companies.addAll(companies);
      });

      // Load products
      await _loadProducts(reset: true);
    } catch (e) {
      if (kDebugMode) rethrow;
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadProducts({bool reset = false}) async {
    if (reset) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _currentPage = 1;
          _hasMoreData = true;
          _products.clear();
          _filteredProducts.clear();
        });
      });
    }

    if (!_hasMoreData) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _isLoading = true;
        _error = null;
      });
    });

    try {
      final products = await WholesalerApiService.getProducts(
        search:
            _searchController.text.isNotEmpty ? _searchController.text : null,
        companyId: _selectedCompany?.id,
        page: _currentPage,
        size: 20,
      );

      setState(() {
        if (reset) {
          _products.clear();
          _filteredProducts.clear();
        }

        _products.addAll(products);
        _filteredProducts.addAll(products);
        _hasMoreData = products.length == 20;
        _currentPage++;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) rethrow;
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _selectProduct(models.Product product) {
    Navigator.of(context).pop(product);
  }

  void _onCompanyFilterChanged(models.Company? company) {
    setState(() {
      _selectedCompany = company;
    });
    _loadProducts(reset: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختيار المنتج'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: Column(
              children: [
                // Search Field
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث عن المنتجات...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _loadProducts(reset: true);
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                // Company Filter
                if (_companies.isNotEmpty) ...[
                  Row(
                    children: [
                      Text(
                        'تصفية حسب الشركة:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: DropdownButtonFormField<models.Company?>(
                          value: _selectedCompany,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          hint: const Text('جميع الشركات'),
                          items: [
                            const DropdownMenuItem<models.Company?>(
                              value: null,
                              child: Text('جميع الشركات'),
                            ),
                            ..._companies.map((company) => DropdownMenuItem(
                                  value: company,
                                  child: Text(company.name),
                                )),
                          ],
                          onChanged: _onCompanyFilterChanged,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading && _products.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null && _products.isEmpty) {
      return _buildErrorState();
    }

    if (_filteredProducts.isEmpty && !_isLoading) {
      return _buildEmptyState();
    }

    return _buildProductList();
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل المنتجات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[800],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            _error ?? 'حدث خطأ غير متوقع',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _loadProducts(reset: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isNotEmpty || _selectedCompany != null
                ? 'لا توجد منتجات تطابق البحث أو التصفية'
                : 'لا توجد منتجات متاحة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProductList() {
    return RefreshIndicator(
      onRefresh: () => _loadProducts(reset: true),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
        itemCount: _filteredProducts.length + (_hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredProducts.length) {
            // Load more indicator
            if (_hasMoreData && !_isLoading) {
              _loadProducts();
            }
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final product = _filteredProducts[index];
          return _ProductListItem(
            product: product,
            onTap: () => _selectProduct(product),
          );
        },
      ),
    );
  }
}

class _ProductListItem extends StatelessWidget {
  final models.Product product;
  final VoidCallback onTap;

  const _ProductListItem({
    required this.product,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: product.imageUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    product.imageUrl!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => const Icon(
                      Icons.inventory_2,
                      color: AppColors.primary,
                      size: 24,
                    ),
                  ),
                )
              : const Icon(
                  Icons.inventory_2,
                  color: AppColors.primary,
                  size: 24,
                ),
        ),
        title: Text(
          product.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (product.barcode.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'الباركود: ${product.barcode}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
            if (product.company != null) ...[
              const SizedBox(height: 4),
              Text(
                'الشركة: ${product.company!.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        isThreeLine: product.barcode.isNotEmpty || product.company != null,
        onTap: onTap,
      ),
    );
  }
}
