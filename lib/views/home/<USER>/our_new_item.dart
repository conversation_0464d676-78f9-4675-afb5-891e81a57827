import 'package:flutter/material.dart';

import '../../../core/components/product_tile_square.dart';
import '../../../core/components/title_and_action_button.dart';
import '../../../core/constants/constants.dart';
import '../../../core/routes/app_routes.dart';
import '../../../services/home_service.dart';

class OurNewItem extends StatefulWidget {
  const OurNewItem({
    super.key,
  });

  @override
  State<OurNewItem> createState() => _OurNewItemState();
}

class _OurNewItemState extends State<OurNewItem> {
  final HomeService _homeService = HomeService();

  @override
  void initState() {
    super.initState();
    // Load new products when component initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _homeService.loadNewProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TitleAndActionButton(
          title: 'منتجاتنا الجديدة',
          onTap: () => Navigator.pushNamed(context, AppRoutes.newItems),
        ),
        ListenableBuilder(
          listenable: _homeService,
          builder: (context, child) {
            // Show loading state
            if (_homeService.isLoadingNewProducts &&
                _homeService.newProducts.isEmpty) {
              return Container(
                height: 200,
                padding: const EdgeInsets.only(left: AppDefaults.padding),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              );
            }

            // Show error state
            if (_homeService.newProductsError != null &&
                _homeService.newProducts.isEmpty) {
              return Container(
                height: 200,
                padding: const EdgeInsets.all(AppDefaults.padding),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 48, color: Colors.grey),
                      const SizedBox(height: 8),
                      Text(
                        'فشل في تحميل المنتجات الجديدة',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () =>
                            _homeService.loadNewProducts(forceRefresh: true),
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                ),
              );
            }

            // Show products or fallback to dummy data
            final products = _homeService.newProducts.isNotEmpty
                ? _homeService.newProductsForUI()
                : Dummy.products; // Fallback to dummy data

            return SingleChildScrollView(
              padding: const EdgeInsets.only(left: AppDefaults.padding),
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  products.length,
                  (index) {
                    final product = products[index];
                    // Check if this is API data (ProductModel from ProductWithPricing)
                    final productId = _homeService.newProducts.isNotEmpty
                        ? _homeService.newProducts[index].id
                        : null;
                    return ProductTileSquare(
                      data: product,
                      productId: productId,
                    );
                  },
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
