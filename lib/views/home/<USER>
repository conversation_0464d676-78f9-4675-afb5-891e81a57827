import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../api/wholesaler_api.dart';
import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/constants/app_icons.dart';
import '../../core/routes/app_routes.dart';
import '../../models/cart_models.dart';
import '../../services/app_services.dart';
import 'components/wholesaler_header.dart';
import 'components/wholesaler_product_card.dart';

class WholesalerProductsPage extends StatefulWidget {
  final int wholesalerId;
  final bool isHomePage;

  const WholesalerProductsPage({
    super.key,
    required this.wholesalerId,
    this.isHomePage = false,
  });

  @override
  State<WholesalerProductsPage> createState() => _WholesalerProductsPageState();
}

class _WholesalerProductsPageState extends State<WholesalerProductsPage> {
  late final _cartService = AppServices().cartService;

  WholesalerInfo? _wholesalerInfo;
  List<WholesalerItemResponse> _products = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;
  String _searchQuery = '';

  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  /// Exit wholesaler and clear cart
  Future<void> _exitWholesaler() async {
    await _cartService.clearCart();
    // If this page was accessed directly (not home mode), pop to previous screen
    if (!widget.isHomePage && mounted) {
      Navigator.pop(context);
    }
  }

  @override
  void initState() {
    super.initState();
    _loadWholesalerData();
    _cartService.addListener(_onCartChanged);
  }

  @override
  void dispose() {
    _cartService.removeListener(_onCartChanged);
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onCartChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadWholesalerData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Load wholesaler info and products in parallel
      final results = await Future.wait([
        WholesalerApiService.getWholesalerById(widget.wholesalerId),
        WholesalerApiService.getWholesalerItems(widget.wholesalerId),
      ]);

      _wholesalerInfo = results[0] as WholesalerInfo;
      _products = results[1] as List<WholesalerItemResponse>;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refresh() async {
    await _loadWholesalerData();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  List<WholesalerItemResponse> get _filteredProducts {
    if (_searchQuery.isEmpty) {
      return _products;
    }
    return _products.where((product) {
      return product.productName.toLowerCase().contains(_searchQuery) ||
          (product.company?.title.toLowerCase().contains(_searchQuery) ??
              false) ||
          (product.category?.title.toLowerCase().contains(_searchQuery) ??
              false);
    }).toList();
  }

  Future<void> _addToCart(WholesalerItemResponse product) async {
    if (_wholesalerInfo == null) return;

    final result = await _cartService.addToCart(
      wholesalerItemId: product.id,
      wholesalerInfo: _wholesalerInfo!,
      quantity: 1,
    );

    if (result.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة ${product.productName} إلى السلة'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'عرض السلة',
            textColor: Colors.white,
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.cartPage);
            },
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.message ?? 'فشل في إضافة المنتج إلى السلة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Navigate to product details in wholesaler mode
  void _navigateToProductDetails(WholesalerItemResponse product) {
    Navigator.pushNamed(
      context,
      AppRoutes.productDetails,
      arguments: {
        'productId': product.productId,
        'wholesalerMode': true,
        'wholesalerId': widget.wholesalerId,
        'wholesalerItemId': product.id,
        'wholesalerInfo': _wholesalerInfo,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return _buildErrorState();
    }

    return RefreshIndicator(
      onRefresh: _refresh,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar
          _buildAppBar(),

          // Wholesaler header
          if (_wholesalerInfo != null)
            SliverToBoxAdapter(
              child: WholesalerHeader(
                wholesaler: _wholesalerInfo!,
                cartItemCount: _cartService.itemCount,
                totalPrice: _cartService.totalPrice,
                onExit: _exitWholesaler,
              ),
            ),

          // Search bar
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: TextField(
                controller: _searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText:
                      'ابحث في منتجات ${_wholesalerInfo?.title ?? "التاجر"}',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _onSearchChanged('');
                          },
                          icon: const Icon(Icons.clear),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ),

          // Products grid
          SliverToBoxAdapter(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              child: Text(
                'المنتجات (${_filteredProducts.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ),

          const SliverToBoxAdapter(child: SizedBox(height: 12)),

          if (_filteredProducts.isEmpty)
            SliverFillRemaining(
              child: _buildEmptyState(),
            )
          else
            SliverPadding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.45,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final product = _filteredProducts[index];
                    return WholesalerProductCard(
                      product: product,
                      onAddToCart: () => _addToCart(product),
                      onTap: () => _navigateToProductDetails(product),
                      isInCart: _cartService.isInCart(product.id),
                      cartQuantity: _cartService.getItemQuantity(product.id),
                    );
                  },
                  childCount: _filteredProducts.length,
                ),
              ),
            ),

          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    if (widget.isHomePage) {
      // Home page style app bar
      return SliverAppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, AppRoutes.drawerPage);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF2F6F3),
              shape: const CircleBorder(),
            ),
            child: SvgPicture.asset(AppIcons.sidebarIcon),
          ),
        ),
        floating: true,
        title: SvgPicture.asset(
          "assets/images/app_logo.svg",
          height: 32,
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
            child: ElevatedButton(
              onPressed: () {
                Navigator.pushNamed(context, AppRoutes.search);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF2F6F3),
                shape: const CircleBorder(),
              ),
              child: SvgPicture.asset(AppIcons.search),
            ),
          ),
        ],
      );
    } else {
      // Regular page style app bar
      return SliverAppBar(
        leading: const AppBackButton(),
        floating: true,
        title: Text(_wholesalerInfo?.title ?? 'منتجات التاجر'),
        actions: [
          // Cart icon with badge
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  Navigator.pushNamed(context, AppRoutes.cartPage);
                },
                icon: const Icon(Icons.shopping_cart_outlined),
              ),
              if (_cartService.itemCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${_cartService.itemCount}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 8),
        ],
      );
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل منتجات التاجر',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadWholesalerData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search_off, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'لا توجد منتجات تطابق البحث'
                : 'لا توجد منتجات متاحة',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'جرب البحث بكلمات مختلفة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                _onSearchChanged('');
              },
              child: const Text('مسح البحث'),
            ),
          ],
        ],
      ),
    );
  }
}
