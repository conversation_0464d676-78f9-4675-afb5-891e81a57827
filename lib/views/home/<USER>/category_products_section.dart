import 'package:flutter/material.dart';
import '../../../api/wholesaler_api.dart';
import '../../../core/constants/app_defaults.dart';
import 'wholesaler_product_card.dart';

class CategoryProductsSection extends StatelessWidget {
  final String categoryTitle;
  final List<WholesalerItemResponse> products;
  final Function(WholesalerItemResponse) onAddToCart;
  final Function(WholesalerItemResponse) onProductTap;
  final bool Function(int) isInCart;
  final int Function(int) getCartQuantity;

  const CategoryProductsSection({
    super.key,
    required this.categoryTitle,
    required this.products,
    required this.onAddToCart,
    required this.onProductTap,
    required this.isInCart,
    required this.getCartQuantity,
  });

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category header
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDefaults.padding,
            vertical: 8,
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  categoryTitle,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${products.length}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
        ),

        // Horizontal scrollable products list
        SizedBox(
          height: 390, // Fixed height for the horizontal list
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDefaults.padding,
            ),
            itemCount: products.length,
            itemBuilder: (context, index) {
              final product = products[index];
              return Container(
                width: 210, // Fixed width for each product card
                margin: EdgeInsets.only(
                  left: index < products.length - 1
                      ? 12
                      : 0, // Changed to left for RTL
                ),
                child: WholesalerProductCard(
                  product: product,
                  onAddToCart: () => onAddToCart(product),
                  onTap: () => onProductTap(product),
                  isInCart: isInCart(product.id),
                  cartQuantity: getCartQuantity(product.id),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 16),
      ],
    );
  }
}
