import 'package:flutter/material.dart';

import '../../../api/regions.dart';
import '../../../core/constants/constants.dart';
import '../../../services/app_services.dart';

class RegionSelectionDialog extends StatefulWidget {
  const RegionSelectionDialog({super.key});

  @override
  State<RegionSelectionDialog> createState() => _RegionSelectionDialogState();
}

class _RegionSelectionDialogState extends State<RegionSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  late final _regionService = AppServices().regionService;
  List<RegionModel> _filteredRegions = [];
  String _selectedType = 'ALL'; // ALL, COUNTRY, STATE, DISTRICT

  @override
  void initState() {
    super.initState();
    _regionService.addListener(_onRegionServiceChanged);
    // Defer loading regions until after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRegions();
    });
  }

  void _onRegionServiceChanged() {
    if (mounted) {
      _filterRegions(_regionService.allRegions);
    }
  }

  void _loadRegions() {
    _regionService.fetchRegions();
  }

  void _filterRegions(List<RegionModel> allRegions) {
    List<RegionModel> regions;

    switch (_selectedType) {
      case 'COUNTRY':
        regions = allRegions.where((r) => r.isCountry).toList();
        break;
      case 'STATE':
        regions = allRegions.where((r) => r.isState).toList();
        break;
      case 'DISTRICT':
        regions = allRegions.where((r) => r.isDistrict).toList();
        break;
      default:
        regions = allRegions;
    }

    final query = _searchController.text.toLowerCase();
    if (query.isNotEmpty) {
      regions = regions.where((region) {
        return region.name.toLowerCase().contains(query) ||
            region.hierarchicalName.toLowerCase().contains(query);
      }).toList();
    }

    setState(() {
      _filteredRegions = regions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  'اختر منطقتك',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search field
            TextField(
              controller: _searchController,
              onChanged: (_) => _filterRegions(_regionService.allRegions),
              decoration: InputDecoration(
                hintText: 'ابحث عن منطقة...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Filter chips
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('الكل', 'ALL'),
                  _buildFilterChip('الدول', 'COUNTRY'),
                  _buildFilterChip('المحافظات', 'STATE'),
                  _buildFilterChip('المناطق', 'DISTRICT'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Content
            Expanded(
              child: _buildContent(),
            ),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _regionService.hasSelectedRegion
                        ? () => Navigator.of(context).pop(true)
                        : null,
                    child: const Text('تأكيد'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String type) {
    final isSelected = _selectedType == type;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) {
          setState(() {
            _selectedType = type;
          });
          _filterRegions(_regionService.allRegions);
        },
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
      ),
    );
  }

  Widget _buildContent() {
    if (_regionService.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_regionService.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'فشل في تحميل المناطق',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _regionService.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _regionService.fetchRegions(forceRefresh: true),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_filteredRegions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.location_off, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'لا توجد مناطق',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على مناطق مطابقة لبحثك',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredRegions.length,
      itemBuilder: (context, index) {
        final region = _filteredRegions[index];
        final isSelected = _regionService.selectedRegion?.id == region.id;

        return ListTile(
          title: Text(region.name),
          subtitle: region.hierarchicalName != region.name
              ? Text(region.hierarchicalName)
              : null,
          leading: CircleAvatar(
            backgroundColor: _getRegionTypeColor(region.type),
            child: Text(
              _getRegionTypeInitial(region.type),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          trailing: isSelected
              ? Icon(Icons.check_circle, color: AppColors.primary)
              : null,
          selected: isSelected,
          selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
          onTap: () async {
            try {
              await _regionService.selectRegion(region);
              setState(() {}); // Update UI
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('فشل في حفظ المنطقة: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
        );
      },
    );
  }

  Color _getRegionTypeColor(String type) {
    switch (type) {
      case 'COUNTRY':
        return Colors.blue;
      case 'STATE':
        return Colors.green;
      case 'DISTRICT':
        return Colors.orange;
      case 'CITY':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getRegionTypeInitial(String type) {
    switch (type) {
      case 'COUNTRY':
        return 'د'; // دولة
      case 'STATE':
        return 'م'; // محافظة
      case 'DISTRICT':
        return 'ن'; // منطقة
      case 'CITY':
        return 'ن'; // محافظة
      default:
        return '؟';
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _regionService.removeListener(_onRegionServiceChanged);
    super.dispose();
  }
}
