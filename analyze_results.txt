[info] Unnecessary 'this.' qualifier (/Users/<USER>/Desktop/Pro_Grocery-main/lib/models/cart_models.dart:38:28)
[warning] Unused import: 'stores_api.dart' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:2:8)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:58:7)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:78:7)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:99:7)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:119:7)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:146:7)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/api/orders_api.dart:165:7)
[warning] The value of the field '_isAddingToCart' isn't used (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[warning] The value of the field '_isLoadingMore' isn't used (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] The private field _isLoadingMore could be 'final' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:31:31)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:32:31)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:36:36)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:56:45)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:113:51)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:257:48)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:75:42)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_header.dart:263:25)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/region_selector.dart:77:18)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/region_selector.dart:106:15)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_product_card.dart:92:51)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_product_card.dart:243:57)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_product_card.dart:30:15)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_selection_dialog.dart:90:42)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_selection_dialog.dart:144:49)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/wholesaler_selection_dialog.dart:95:19)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/product_filters_dialog.dart:90:37)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/region_selection_dialog.dart:263:17)
[info] Don't use 'BuildContext's across async gaps, guarded by an unrelated 'mounted' check (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/home/<USER>/region_selection_dialog.dart:273:38)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/order/my_order_page.dart:44:7)
[info] Don't invoke 'print' in production code (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/order/order_details.dart:55:7)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/order/components/order_details_total_amount_and_paid.dart:84:44)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/components/profile_header.dart:103:43)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/components/profile_header.dart:119:43)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/profile_edit_page.dart:228:50)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/profile_edit_page.dart:231:54)
[info] Don't use 'BuildContext's across async gaps (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/profile_edit_page.dart:108:17)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/profile/profile_edit_page.dart:235:31)
[info] 'color' is deprecated and shouldn't be used (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/wholesaler_cart_item_tile.dart:124:23)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/delivery_time_selector.dart:42:33)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/cart_wholesaler_header.dart:26:33)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:8:9)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:9:18)
[error] The name 'StoreResponse' isn't a type, so it can't be used as a type argument (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:24:8)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:27:3)
[error] The name 'StoreResponse' isn't a type, so it can't be used as a type argument (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:65:44)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:65:3)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:74:27)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:84:21)
[error] The name 'StoreResponse' isn't a type, so it can't be used as a type argument (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:104:39)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:187:26)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:366:38)
[error] Undefined name 'StoreApiService' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:43:28)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:130:33)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:368:7)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/store_selector.dart:369:18)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/components/cart_totals_card.dart:32:33)
[error] The method 'CreateStoreRequest' isn't defined for the type '_CreateStoreDialogState' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/dialogs/create_store_dialog.dart:137:23)
[error] Undefined name 'StoreApiService' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/dialogs/create_store_dialog.dart:146:27)
[info] 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/dialogs/change_wholesaler_dialog.dart:39:38)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/dialogs/change_wholesaler_dialog.dart:42:22)
[error] Undefined class 'StoreResponse' (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/checkout_page.dart:25:3)
[warning] The value of the local variable 'regionService' isn't used (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/checkout_page.dart:47:11)
[warning] The value of the local variable 'orderResponse' isn't used (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/checkout_page.dart:77:13)
[warning] The value of the local variable 'cartService' isn't used (/Users/<USER>/Desktop/Pro_Grocery-main/lib/views/cart/checkout_page.dart:123:11)
[info] Use 'rethrow' to rethrow a caught exception (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/home_service.dart:94:7)
[info] Use 'rethrow' to rethrow a caught exception (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/home_service.dart:104:7)
[info] Use 'rethrow' to rethrow a caught exception (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/home_service.dart:114:7)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/cart_service.dart:116:14)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/cart_service.dart:154:14)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/cart_service.dart:186:16)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/cart_service.dart:213:14)
[info] Use 'const' with the constructor to improve performance (/Users/<USER>/Desktop/Pro_Grocery-main/lib/services/cart_service.dart:240:14)