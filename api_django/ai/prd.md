# Product Requirements Document (PRD) - TagerPlus Backend

## 1. Overview
- **Product Name:** TagerPlus
- **Date:** 2025-04-13
- **Prepared by:** seifalmotaz
- **Version:** 1.1

### Purpose
TagerPlus is a B2B application that connects wholesalers with resellers. The backend, built with Django and Django REST Framework (DRF), features a custom authentication system using phone-based OTP verification for initial user sign-ups. The system supports:
- **Wholesalers:** Who manage single stores, set regional pricing, and define minimum order constraints.
- **Resellers:** Who manage multiple stores and view pricing specific to their delivery regions.

TagerPlus aims to streamline the interaction between wholesalers and resellers by addressing the unique needs of each user group.

---

## 2. Vision and Goals

### Product Vision
Create an efficient, scalable, and secure B2B platform that simplifies transactions between wholesalers and resellers. The system will include regional pricing, minimum order constraints, and tailored experiences for resellers managing multiple stores and wholesalers managing regional pricing.

### Goals and Objectives
- **Enable regional pricing:** Allow wholesalers to set different prices for the same product across regions.
- **Support multiple stores for resellers:** Provide resellers with the ability to manage multiple stores and view pricing based on their delivery regions.
- **Enhance security:** Implement a custom authentication system with OTP verification to safeguard user access.
- **Optimize scalability:** Utilize a modular architecture to support independent scaling of services.
- **Facilitate seamless transactions:** Ensure a robust and user-friendly backend for streamlined communication between wholesalers and resellers.

---

## 3. Problem Statement
- **Current Challenges:** 
  - Limited ability for wholesalers to set region-specific pricing and order constraints.
  - Lack of support for resellers managing multiple stores and viewing pricing based on delivery regions.
  - Security concerns regarding unauthorized access and data breaches.
  
- **User Needs:** 
  - **Wholesalers:** Need to manage single stores, set regional pricing, and define minimum order constraints.
  - **Resellers:** Require the ability to manage multiple stores and view region-specific pricing for deliveries.
  
- **Opportunity:** 
  - Streamline the connection between wholesalers and resellers by addressing regional pricing and delivery-specific pricing challenges.
  - Build a secure, scalable backend for efficient and tailored user experiences.

---

## 4. User Stories and Use Cases

### User Stories

1. **As a wholesaler, I want to set different prices for the same product across regions so that I can optimize pricing based on local market conditions.**
2. **As a wholesaler, I want to define minimum charges and minimum items per region so that I can maintain profitability in each region.**
3. **As a reseller, I want to manage multiple stores so that I can organize orders based on my different business locations.**
4. **As a reseller, I want to see product pricing based on the delivery region of my store so that I can make accurate purchasing decisions.**
5. **As a new user, I want to sign up using my phone number with OTP verification so that I can securely create an account.**
6. **As a returning user, I want to log in seamlessly using my credentials and receive a secure token for subsequent requests.**

### Use Cases

- **Wholesaler Regional Pricing Management:** 
  - **Trigger:** Log in as a wholesaler.
  - **Interaction:** Add or update product pricing for specific regions; set minimum charges and minimum items for orders in each region.
  - **Outcome:** Resellers see pricing and constraints specific to their store’s delivery region.
  
- **Reseller Multi-Store Management:** 
  - **Trigger:** Log in as a reseller.
  - **Interaction:** Add or manage multiple stores; assign delivery regions to each store.
  - **Outcome:** Store-specific pricing and regional constraints are applied when placing orders.
  
- **Custom Phone-Based Authentication:**
  - **Trigger:** A user initiates sign-up using their phone number.
  - **Interaction:** The user receives an OTP for verification; the system validates the OTP and creates an account.
  - **Outcome:** A custom user account is created, and a JWT token is issued for secured session management.

---

## 5. Functional Requirements

### Service Breakdown
1. **Auth Service**
   - **Feature:** Custom JWT Authentication with Phone-Based OTP Verification.
   - **Description:** 
     - Handle user registration using phone numbers.
     - Verify new users with OTP during the first sign-up.
     - Provide secure JWT token management for subsequent authentication.
   - **Priority:** High
   - **Dependencies:** User Service, API Gateway

2. **User Service**
   - **Feature:** User Management (Wholesaler & Reseller).
   - **Description:** Manage user profiles, roles, and permissions. Support multi-store management for resellers and single-store management for wholesalers.
   - **Priority:** High
   - **Dependencies:** Auth Service

3. **Products Service**
   - **Feature:** Product and Inventory Management.
   - **Description:** Manage the pre-existing product catalog. Allow wholesalers to set region-specific pricing and constraints.
   - **Priority:** High
   - **Dependencies:** User Service, Auth Service

4. **Ordering Service**
   - **Feature:** Order Processing and Management.
   - **Description:** Allow resellers to place orders with store-specific pricing and constraints based on delivery regions.
   - **Priority:** High
   - **Dependencies:** Products Service, User Service

5. **Wholesaler Service**
   - **Feature:** Regional Pricing and Constraints Management.
   - **Description:** Enable wholesalers to add and manage regional prices and order constraints for their single store.
   - **Priority:** Medium
   - **Dependencies:** Products Service, User Service

---

## 6. Non-Functional Requirements

- **Performance:** 
  - All services should respond to API requests within an acceptable timeframe (<500ms under nominal loads).
  
- **Scalability:**
  - Services are containerized and can scale horizontally.
  
- **Security:**
  - Custom phone-based authentication with OTP for initial sign-up.
  - JWT for secure, stateless session management.
  - All sensitive endpoints must use HTTPS.
  - Regular security audits and compliance checks.
  
- **Usability:**
  - Clear API documentation for third-party integrations.
  
- **Compatibility:**
  - Compatible with major web and mobile clients.
  - API versioning to support backward compatibility.

---

## 7. Technical Requirements

- **Architecture Overview:**
  - Microservice architecture with clearly defined service boundaries.
  - Backend built on Django with Django REST Framework.
  - Stateless authentication using custom JWT tokens coupled with OTP-based phone verification for new users.

- **Technology Stack:**
  - **Backend Framework:** Django, Django REST Framework
  - **Authentication:** Custom Auth using phone-based OTP and JWT for subsequent requests.
  - **Database:** PostgreSQL (or another relational DB)
  - **Containerization:** Docker for deployment and scaling
  
- **API Requirements:**
  - RESTful endpoints for each service.
  - Unified API gateway for routing requests and aggregating responses.
  
- **Data Storage:**
  - Centralized relational database for transactional data.
  - Caching for frequently accessed data to improve performance.

---

## 8. Timeline and Milestones
- **Phase 1:** Planning and Requirement Analysis – 2025-04-13 to 2025-04-27
- **Phase 2:** Design and Architecture – 2025-04-28 to 2025-05-12
- **Phase 3:** Development and Service Implementation – 2025-05-13 to 2025-06-30
- **Phase 4:** Testing and Quality Assurance – 2025-07-01 to 2025-07-15
- **Phase 5:** Deployment and Feedback – 2025-07-16 to 2025-07-31

---

## 9. Risks and Mitigation Strategies
- **Security Risks:** Ensure robust practices with the custom authentication system, including secure OTP delivery and JWT management, with regular security audits.
- **Scalability Risks:** Use container orchestration (e.g., Kubernetes) for horizontal scaling.
- **Integration Risks:** Maintain detailed API contracts and continuous integration testing.
- **User Adoption:** Solicit early feedback from both wholesalers and resellers to iterate quickly.

---

## 10. Appendices
- **References:** 
  - Django documentation: https://docs.djangoproject.com/
  - DRF documentation: https://www.django-rest-framework.org/
  - JWT Best Practices: https://jwt.io/introduction
  - OTP and Phone-based Authentication: Refer to relevant security protocols and packages.
- **Glossary:**
  - **JWT:** JSON Web Token used for secure stateless session management.
  - **OTP:** One-Time Password used for secure phone-based user verification.
  - **DRF:** Django REST Framework.
  - **B2B:** Business-to-Business platform.