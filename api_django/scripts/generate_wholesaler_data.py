#!/usr/bin/env python
"""
Script to generate dummy wholesaler data including stores, min charges for regions and items.
Uses factory_boy for model instance generation.

Note: This script assumes that products and regions already exist in the database.
"""

import os
import sys
import random
import django
from datetime import timedelta
from decimal import Decimal

# Set up Django environment
sys.path.append("/Users/<USER>/Desktop/mintager/api_django/src")
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
django.setup()

import factory
from factory.django import DjangoModelFactory
from django.utils import timezone
from django.core.files.base import ContentFile

from accounts.models import CustomUser
from products.models import Product, Region
from wholesalers.models import (
    Wholesaler,
    RegionMinCharge,
    Item,
    WholeSalerCategory,
    PricingType,
    MeasurementUnit,
)


def generate_image_file(filename="test.png", width=100, height=100):
    """
    Generate a simple image file for testing

    Args:
        filename: Name of the image file
        width: Width of the image in pixels
        height: Height of the image in pixels

    Returns:
        ContentFile: A Django ContentFile containing the image data
    """
    try:
        from PIL import Image
        import io

        # Create a simple colored image
        color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        image = Image.new("RGB", (width, height), color=color)
        image_io = io.BytesIO()
        image.save(image_io, format="PNG")
        image_io.seek(0)

        return ContentFile(image_io.getvalue(), name=filename)
    except ImportError:
        print("Warning: PIL not installed. Using empty image file.")
        return ContentFile(b"", name=filename)


class WholesalerFactory(DjangoModelFactory):
    """Factory for creating Wholesaler instances"""

    class Meta:
        model = Wholesaler

    category = factory.Iterator([choice[0] for choice in WholeSalerCategory.choices])

    # Use existing users or create one if needed
    @factory.lazy_attribute
    def user(self):
        users = CustomUser.objects.all()
        if users.exists():
            return random.choice(users)
        return CustomUser.objects.create_user(
            username=f"wholesaler_user_{random.randint(1000, 9999)}",
            email=f"wholesaler_user_{random.randint(1000, 9999)}@example.com",
            password="password123",
            phone=f"+20123456789{random.randint(0, 9)}",
            phone_verified=True,
        )

    title = factory.Faker("company")

    @factory.lazy_attribute
    def username(self):
        # Create a username from the title
        base_username = self.title.lower().replace(" ", "_")[:20]
        unique_suffix = random.randint(100, 999)
        return f"{base_username}_{unique_suffix}"

    # Generate background image and logo
    @factory.lazy_attribute
    def background_image(self):
        return generate_image_file(
            f"wholesaler_bg_{random.randint(1000, 9999)}.png", 800, 400
        )

    @factory.lazy_attribute
    def logo(self):
        return generate_image_file(
            f"wholesaler_logo_{random.randint(1000, 9999)}.png", 200, 200
        )

    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)
    deleted_at = None


class RegionMinChargeFactory(DjangoModelFactory):
    """Factory for creating RegionMinCharge instances"""

    class Meta:
        model = RegionMinCharge
        django_get_or_create = ("wholesaler", "region")

    wholesaler = factory.SubFactory(WholesalerFactory)

    # Use existing regions or return None
    @factory.lazy_attribute
    def region(self):
        regions = Region.objects.all()
        if regions.exists():
            return random.choice(regions)
        return None

    min_charge = factory.LazyFunction(
        lambda: Decimal(random.uniform(10, 500)).quantize(Decimal("0.01"))
    )
    min_items = factory.LazyFunction(lambda: random.randint(1, 20))

    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)
    deleted_at = None


class ItemFactory(DjangoModelFactory):
    """Factory for creating Item instances"""

    class Meta:
        model = Item
        django_get_or_create = ("wholesaler", "product")

    wholesaler = factory.SubFactory(WholesalerFactory)

    # Use existing products or return None
    @factory.lazy_attribute
    def product(self):
        products = Product.objects.all()
        if products.exists():
            return random.choice(products)
        return None

    base_price = factory.LazyFunction(
        lambda: Decimal(random.uniform(5, 200)).quantize(Decimal("0.01"))
    )
    inventory_count = factory.LazyFunction(lambda: random.randint(0, 1000))

    @factory.lazy_attribute
    def price_expiry(self):
        # Random expiry date between now and 14 days in the future
        days = random.randint(1, 14)
        return timezone.now() + timedelta(days=days)

    @factory.lazy_attribute
    def expires_at(self):
        # Random expiry date between now and 30 days in the future
        days = random.randint(1, 30)
        return timezone.now() + timedelta(days=days)

    created_at = factory.LazyFunction(timezone.now)
    updated_at = factory.LazyFunction(timezone.now)
    deleted_at = None


def generate_dummy_data(
    num_wholesalers=5, min_regions_per_wholesaler=2, min_items_per_wholesaler=5
):
    """
    Generate dummy data for wholesalers, region min charges, and items.

    Args:
        num_wholesalers: Number of wholesalers to create
        min_regions_per_wholesaler: Minimum number of regions per wholesaler
        min_items_per_wholesaler: Minimum number of items per wholesaler
    """
    print(f"Generating {num_wholesalers} wholesalers...")

    # Check if there are products in the database
    product_count = Product.objects.count()
    if product_count == 0:
        print("ERROR: No products found in the database. Cannot create items.")
        return

    # Check if there are regions in the database
    region_count = Region.objects.count()
    if region_count == 0:
        print(
            "ERROR: No regions found in the database. Cannot create region min charges."
        )
        return

    # Create wholesalers
    wholesalers = []
    for _ in range(num_wholesalers):
        wholesaler = WholesalerFactory()
        wholesalers.append(wholesaler)
        print(f"Created wholesaler: {wholesaler.title} ({wholesaler.username})")

    print("\nGenerating region minimum charges...")
    # Create region min charges for each wholesaler
    for wholesaler in wholesalers:
        # Get random number of regions (between min_regions_per_wholesaler and available regions)
        num_regions = min(
            region_count,
            random.randint(min_regions_per_wholesaler, min(10, region_count)),
        )

        # Get random regions
        regions = list(Region.objects.all())
        random.shuffle(regions)
        selected_regions = regions[:num_regions]

        for region in selected_regions:
            region_min_charge = RegionMinChargeFactory(
                wholesaler=wholesaler, region=region
            )
            print(
                f"  {wholesaler.title} in {region.name}: "
                f"${region_min_charge.min_charge} minimum, {region_min_charge.min_items} items minimum"
            )

    print("\nGenerating items for wholesalers...")
    # Create items for each wholesaler
    for wholesaler in wholesalers:
        # Get random number of items (between min_items_per_wholesaler and available products)
        num_items = min(
            product_count,
            random.randint(min_items_per_wholesaler, min(20, product_count)),
        )

        # Get random products
        products = list(Product.objects.all())
        random.shuffle(products)
        selected_products = products[:num_items]

        print(f"  Adding {len(selected_products)} items to {wholesaler.title}:")
        for product in selected_products:
            item = ItemFactory(wholesaler=wholesaler, product=product)
            print(
                f"    - {product.name} - ${item.base_price}, Stock: {item.inventory_count}"
            )

    print("\nDummy data generation complete!")


if __name__ == "__main__":
    # Parse command line arguments
    import argparse

    parser = argparse.ArgumentParser(description="Generate dummy wholesaler data")
    parser.add_argument(
        "--wholesalers", type=int, default=5, help="Number of wholesalers to create"
    )
    parser.add_argument(
        "--regions",
        type=int,
        default=2,
        help="Minimum number of regions per wholesaler",
    )
    parser.add_argument(
        "--items", type=int, default=5, help="Minimum number of items per wholesaler"
    )

    args = parser.parse_args()

    # Generate dummy data
    generate_dummy_data(
        num_wholesalers=args.wholesalers,
        min_regions_per_wholesaler=args.regions,
        min_items_per_wholesaler=args.items,
    )
