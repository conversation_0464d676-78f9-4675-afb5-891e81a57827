// Active Orders Admin - Premium Modern JavaScript

document.addEventListener('DOMContentLoaded', function () {
    // Initialize all functionality
    initializeCopyButtons();
    initializeFilters();
    initializeKeyboardShortcuts();
    initializeTooltips();
    initializeAutoRefresh();
    initializeAnimations();

    console.log('🚀 Active Orders Admin initialized with premium features');
});

// Copy phone numbers functionality with enhanced UX
function initializeCopyButtons() {
    const copyButtons = document.querySelectorAll('.copy-btn');

    copyButtons.forEach(button => {
        button.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const phoneNumber = this.getAttribute('data-phone');

            if (phoneNumber && phoneNumber !== 'N/A' && phoneNumber !== 'Not available') {
                copyToClipboard(phoneNumber, this);
            }
        });
    });
}

// Enhanced clipboard functionality with modern APIs
function copyToClipboard(text, button) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccess(button, text);
        }).catch(err => {
            // Fallback to legacy method
            fallbackCopyToClipboard(text, button);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyToClipboard(text, button);
    }
}

// Global function for admin list view copy buttons
window.copyToClipboard = copyToClipboard;

function fallbackCopyToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess(button, text);
    } catch (err) {
        console.error('Failed to copy text: ', err);
        showCopyError(button);
    }

    document.body.removeChild(textArea);
}

function showCopySuccess(button, text) {
    const originalHTML = button.innerHTML;
    const originalClass = button.className;

    // Show success state
    button.innerHTML = '✅';
    button.classList.add('copy-success');

    // Show premium toast notification
    showToast(`📋 Copied: ${text}`, 'success');

    // Reset after animation
    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.className = originalClass;
    }, 1200);
}

function showCopyError(button) {
    const originalHTML = button.innerHTML;

    button.innerHTML = '❌';
    button.style.background = 'var(--danger-color)';

    showToast('❌ Failed to copy phone number', 'error');

    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.style.background = '';
    }, 1200);
}

// Premium toast notifications
function showToast(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
        existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            <span class="toast-message">${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // Premium toast styles
    toast.style.cssText = `
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: ${type === 'success' ? 'var(--accent-color)' : type === 'error' ? 'var(--danger-color)' : 'var(--primary-color)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-xl);
        z-index: 10000;
        font-weight: 500;
        font-size: 0.875rem;
        opacity: 0;
        transform: translateX(100%) scale(0.8);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        max-width: 400px;
    `;

    // Toast content styling
    const toastContent = toast.querySelector('.toast-content');
    toastContent.style.cssText = `
        display: flex;
        align-items: center;
        gap: 0.75rem;
    `;

    const toastClose = toast.querySelector('.toast-close');
    toastClose.style.cssText = `
        background: none;
        border: none;
        color: white;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0;
        margin-left: auto;
        opacity: 0.7;
        transition: opacity 0.2s ease;
    `;

    toastClose.addEventListener('mouseenter', () => {
        toastClose.style.opacity = '1';
    });

    toastClose.addEventListener('mouseleave', () => {
        toastClose.style.opacity = '0.7';
    });

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0) scale(1)';
    }, 100);

    // Auto-remove after delay
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%) scale(0.8)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 400);
    }, 4000);
}

// Enhanced filters with smooth animations
function initializeFilters() {
    const form = document.getElementById('filters-form');
    const searchInput = document.getElementById('search');
    const filterSelects = document.querySelectorAll('#wholesaler, #status');

    // Auto-submit on select change with loading state
    filterSelects.forEach(select => {
        select.addEventListener('change', function () {
            showLoadingState();
            form.submit();
        });
    });

    // Enhanced search with debounce and loading indicator
    let searchTimeout;
    searchInput.addEventListener('input', function () {
        clearTimeout(searchTimeout);

        // Show typing indicator
        this.style.borderColor = 'var(--primary-color)';

        searchTimeout = setTimeout(() => {
            if (this.value.length >= 3 || this.value.length === 0) {
                showLoadingState();
                form.submit();
            }
        }, 1000);
    });

    // Clear search on Escape with animation
    searchInput.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            this.value = '';
            this.style.borderColor = 'var(--border-color)';
            showLoadingState();
            form.submit();
        }
    });
}

// Show loading state with premium animation
function showLoadingState() {
    const results = document.querySelector('.results');
    if (results) {
        results.classList.add('loading');
        showToast('🔄 Updating results...', 'info');
    }
}

// Enhanced keyboard shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function (e) {
        // Ctrl+F or Cmd+F - Focus search with animation
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('search');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
                searchInput.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    searchInput.style.transform = 'scale(1)';
                }, 200);
            }
        }

        // Ctrl+R or Cmd+R - Refresh with notification
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            showToast('🔄 Refreshing page...', 'info');
            return;
        }

        // Escape - Clear search with animation
        if (e.key === 'Escape') {
            const searchInput = document.getElementById('search');
            if (searchInput && document.activeElement === searchInput) {
                searchInput.value = '';
                searchInput.blur();
                searchInput.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    searchInput.style.transform = 'scale(1)';
                }, 200);
            }
        }
    });
}

// Premium tooltip system
function initializeTooltips() {
    const elementsWithTooltips = document.querySelectorAll('[title]');

    elementsWithTooltips.forEach(element => {
        let tooltip;

        element.addEventListener('mouseenter', function (e) {
            const title = this.getAttribute('title');
            if (!title) return;

            // Create premium tooltip
            tooltip = document.createElement('div');
            tooltip.className = 'premium-tooltip';
            tooltip.textContent = title;
            tooltip.style.cssText = `
                position: absolute;
                background: var(--card-bg);
                color: var(--text-primary);
                padding: 0.75rem 1rem;
                border-radius: var(--radius-lg);
                font-size: 0.75rem;
                font-weight: 500;
                white-space: nowrap;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-5px) scale(0.9);
                transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                pointer-events: none;
                box-shadow: var(--shadow-lg);
                border: 1px solid var(--border-color);
                backdrop-filter: blur(10px);
            `;

            document.body.appendChild(tooltip);

            // Position tooltip
            const rect = this.getBoundingClientRect();
            tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
            tooltip.style.top = `${rect.top - tooltip.offsetHeight - 10}px`;

            // Show tooltip with animation
            setTimeout(() => {
                tooltip.style.opacity = '1';
                tooltip.style.transform = 'translateY(0) scale(1)';
            }, 100);
        });

        element.addEventListener('mouseleave', function () {
            if (tooltip) {
                tooltip.style.opacity = '0';
                tooltip.style.transform = 'translateY(-5px) scale(0.9)';
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 300);
            }
        });
    });
}

// Smart auto-refresh with user activity detection
function initializeAutoRefresh() {
    let refreshInterval;
    let userActivity = Date.now();

    // Track user activity
    ['mousemove', 'keypress', 'click', 'scroll'].forEach(event => {
        document.addEventListener(event, () => {
            userActivity = Date.now();
        });
    });

    // Auto-refresh every 5 minutes if user is inactive for 2 minutes
    function startAutoRefresh() {
        refreshInterval = setInterval(() => {
            if (Date.now() - userActivity > 120000) { // 2 minutes inactive
                showToast('🔄 Auto-refreshing orders...', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        }, 300000); // 5 minutes
    }

    // Start auto-refresh after 1 minute
    setTimeout(startAutoRefresh, 60000);
}

// Initialize premium animations
function initializeAnimations() {
    // Animate cards on load
    const cards = document.querySelectorAll('.stat-card, .filters-section, .results');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Add hover animations to buttons
    const buttons = document.querySelectorAll('.filter-btn, .utility-btn, .copy-btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function () {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function () {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Premium utility functions
function exportToCSV() {
    const table = document.querySelector('.results table');
    if (!table) {
        showToast('❌ No data available to export', 'error');
        return;
    }

    showToast('📊 Preparing CSV export...', 'info');

    let csv = [];
    const rows = table.querySelectorAll('tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('th, td');
        const rowData = [];

        cells.forEach(cell => {
            let content = cell.textContent.trim();
            content = content.replace(/\s+/g, ' ');
            content = content.replace(/"/g, '""');
            rowData.push(`"${content}"`);
        });

        csv.push(rowData.join(','));
    });

    // Create and download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        const timestamp = new Date().toISOString().split('T')[0];
        link.setAttribute('href', url);
        link.setAttribute('download', `active_orders_${timestamp}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showToast('✅ CSV export completed successfully!', 'success');
    } else {
        showToast('❌ CSV export not supported in this browser', 'error');
    }
}

function printOrders() {
    showToast('🖨️ Preparing print layout...', 'info');

    const printContent = document.querySelector('.admin-active-orders').cloneNode(true);

    // Remove unnecessary elements for printing
    const elementsToRemove = [
        '.utility-actions',
        '.filters-section',
        '.copy-btn',
        '.pagination'
    ];

    elementsToRemove.forEach(selector => {
        const elements = printContent.querySelectorAll(selector);
        elements.forEach(el => el.remove());
    });

    // Create premium print window
    const printWindow = window.open('', '_blank');
    const timestamp = new Date().toLocaleDateString();

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Active Orders Report - ${timestamp}</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
                
                body { 
                    font-family: 'Inter', Arial, sans-serif; 
                    margin: 20px; 
                    color: #1f2937;
                    line-height: 1.6;
                }
                
                .admin-active-orders h1 { 
                    color: #111827; 
                    margin-bottom: 30px; 
                    text-align: center;
                    font-size: 28px;
                    font-weight: 700;
                }
                
                .summary-stats { 
                    display: flex; 
                    gap: 20px; 
                    margin-bottom: 30px; 
                    justify-content: center;
                }
                
                .stat-card { 
                    border: 2px solid #e5e7eb; 
                    padding: 20px; 
                    border-radius: 12px; 
                    text-align: center;
                    min-width: 150px;
                }
                
                .stat-number { 
                    font-size: 32px; 
                    font-weight: 800; 
                    color: #6366f1; 
                    margin-bottom: 8px;
                }
                
                .stat-label { 
                    font-size: 12px; 
                    color: #6b7280; 
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                }
                
                table { 
                    width: 100%; 
                    border-collapse: collapse; 
                    margin-top: 30px; 
                    font-size: 12px;
                }
                
                th, td { 
                    padding: 12px 8px; 
                    text-align: left; 
                    border-bottom: 1px solid #e5e7eb; 
                    vertical-align: top;
                }
                
                th { 
                    background-color: #f9fafb; 
                    font-weight: 700; 
                    color: #374151;
                    text-transform: uppercase;
                    font-size: 11px;
                    letter-spacing: 0.05em;
                }
                
                .status-badge { 
                    padding: 4px 8px; 
                    border-radius: 6px; 
                    font-size: 10px; 
                    font-weight: 700;
                    text-transform: uppercase;
                }
                
                .status-pending { background: #fef3c7; color: #92400e; }
                .status-processing { background: #dbeafe; color: #1e40af; }
                .status-shipped { background: #e9d5ff; color: #7c3aed; }
                
                .price { color: #10b981; font-weight: 700; }
                
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            ${printContent.innerHTML}
            <div style="margin-top: 30px; text-align: center; color: #6b7280; font-size: 12px;">
                Generated on ${new Date().toLocaleString()}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // Wait for content to load, then print
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
        showToast('✅ Print dialog opened successfully!', 'success');
    }, 500);
}

function refreshPage() {
    showToast('🔄 Refreshing page...', 'info');
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Enhanced error handling
window.addEventListener('error', function (e) {
    console.error('JavaScript error:', e.error);
    showToast('❌ An error occurred. Please refresh the page.', 'error');
});

// Performance monitoring
if (window.performance && window.performance.mark) {
    window.performance.mark('active-orders-premium-js-loaded');

    // Log performance metrics
    setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            console.log(`⚡ Page loaded in ${Math.round(navigation.loadEventEnd - navigation.loadEventStart)}ms`);
        }
    }, 1000);
}

// Service Worker registration for offline support (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function () {
        // Service worker can be implemented for offline functionality
        console.log('🚀 Premium Active Orders Admin ready for service worker');
    });
} 