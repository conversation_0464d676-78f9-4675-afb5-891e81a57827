from datetime import datetime, timedelta, timezone
from typing import Optional
from dataclasses import dataclass
import jwt
from accounts.models import CustomUser
from core import settings
import logging

logger = logging.getLogger(__name__)


@dataclass
class JWTTokenData:
    """Dataclass for structured JWT token data"""

    user_id: int
    phone: str
    is_phone_verified: bool
    wholesaler_id: Optional[int] = None
    exp: datetime = None
    iat: datetime = None

    def to_dict(self) -> dict:
        """Convert to dictionary for JWT encoding"""
        return {
            "user_id": self.user_id,
            "phone": self.phone,
            "is_phone_verified": self.is_phone_verified,
            "wholesaler_id": self.wholesaler_id,
            "exp": self.exp,
            "iat": self.iat,
        }


def generate_jwt_token(user: CustomUser) -> str:
    """Generate a secure JWT token with user and wholesaler data"""
    now = datetime.now(timezone.utc)

    # Get wholesaler ID if user has one
    try:
        # Check if user has any active wholesalers
        wholesaler = user.wholesalers.filter(deleted_at__isnull=True).first()
        wholesaler_id = wholesaler.id if wholesaler else None
    except Exception as e:
        logger.warning(f"Error checking wholesaler for user {user.id}: {str(e)}")
        wholesaler_id = None

    # Create structured token data
    token_data = JWTTokenData(
        user_id=user.id,
        phone=user.phone,
        is_phone_verified=user.phone_verified,
        wholesaler_id=wholesaler_id,
        exp=now + timedelta(days=7),  # Token valid for 7 days
        iat=now,
    )

    return jwt.encode(token_data.to_dict(), settings.SECRET_KEY, algorithm="HS256")
