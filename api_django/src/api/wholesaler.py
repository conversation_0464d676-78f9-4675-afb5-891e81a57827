from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from ninja import Schema
from django.shortcuts import get_object_or_404

from products.schemas import CategoryOut, CompanyOut
from wholesalers.models import RegionMinCharge, Wholesaler, Item


class WholesalerItemOut(Schema):
    id: int
    product_id: int
    product_name: str
    price: Decimal
    inventory_count: int
    price_expiry: Optional[datetime] = None
    image_url: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: int


class WholesalerOut(Schema):
    id: int
    title: str
    username: str
    logo_url: Optional[str] = None
    background_image_url: Optional[str] = None


class WholesalerMinChargeOut(Schema):
    id: int
    min_charge: Decimal
    min_items: int


# GET: /api/v2/wholesalers/{wholesaler_id}
def get_wholesaler_by_id(request, wholesaler_id: int) -> WholesalerOut:
    """
    Get a wholesaler by its ID.
    """
    wholesaler = get_object_or_404(
        Wholesaler, id=wholesaler_id, deleted_at__isnull=True
    )
    return WholesalerOut(
        id=wholesaler.id,
        title=wholesaler.title,
        username=wholesaler.username,
        logo_url=wholesaler.logo.url if wholesaler.logo else None,
        background_image_url=wholesaler.background_image.url
        if wholesaler.background_image
        else None,
    )


# GET: /api/v2/wholesalers/{wholesaler_id}/min-charge/{region_id}
def get_wholesaler_min_charge(
    request, wholesaler_id: int, region_id: int
) -> WholesalerMinChargeOut:
    """
    Get the minimum charge for a specific wholesaler.
    """
    wholesaler_min_charge = get_object_or_404(
        RegionMinCharge,
        wholesaler_id=wholesaler_id,
        region_id=region_id,
        deleted_at__isnull=True,
    )
    return WholesalerMinChargeOut(
        id=wholesaler_min_charge.id,
        min_charge=wholesaler_min_charge.min_charge,
        min_items=wholesaler_min_charge.min_items,
    )


# GET: /api/v2/wholesalers/{wholesaler_id}/items
def get_wholesaler_items(request, wholesaler_id: int) -> List[WholesalerItemOut]:
    """
    Get all items (products with pricing) for a specific wholesaler.

    Args:
        wholesaler_id: The ID of the wholesaler to get items for

    Returns:
        List of WholesalerItemOut: Items with product details, pricing, and inventory info
    """
    # Verify wholesaler exists and is not deleted
    wholesaler = get_object_or_404(
        Wholesaler, id=wholesaler_id, deleted_at__isnull=True
    )

    # Get all items for this wholesaler with related product, company, and category data
    items = (
        Item.objects.select_related("product", "product__company", "product__category")
        .filter(
            wholesaler=wholesaler,
            deleted_at__isnull=True,
            product__deleted_at__isnull=True,
        )
        .order_by("product__name")
    )

    # Transform items to schema format
    result = []
    for item in items:
        product = item.product

        # Handle company data
        company = None
        company_id = None
        if product.company and not product.company.deleted_at:
            company_id = product.company.id
            company = CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
                slug=product.company.slug,
            )

        # Handle category data
        category = None
        category_id = None
        if product.category and not product.category.deleted_at:
            category_id = product.category.id
            category = CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
                slug=product.category.slug,
            )

        # Handle product image URL
        image_url = None
        if product.image and product.image.name:
            image_url = product.image.url

        # Create the item output
        item_out = WholesalerItemOut(
            id=item.id,
            product_id=product.id,
            product_name=product.name,
            price=item.base_price,
            inventory_count=item.inventory_count,
            price_expiry=item.price_expiry,
            image_url=image_url,
            company_id=company_id,
            category_id=category_id,
            company=company,
            category=category,
            unit=product.unit,
            unit_count=int(product.unit_count),
        )

        result.append(item_out)

    return result


# GET: /api/v2/wholesalers/{wholesaler_id}/items/{wholesaler_item_id}
def get_wholesaler_item_by_id(request, wholesaler_item_id: int) -> WholesalerItemOut:
    """
    Get a specific item from a wholesaler's inventory by item ID.

    Args:
        wholesaler_item_id: The ID of the wholesaler item to retrieve

    Returns:
        WholesalerItemOut: Item with product details, pricing, and inventory info
    """
    # Get the item with related product, company, and category data
    item = get_object_or_404(
        Item.objects.select_related(
            "product", "product__company", "product__category", "wholesaler"
        ),
        id=wholesaler_item_id,
        deleted_at__isnull=True,
        product__deleted_at__isnull=True,
        wholesaler__deleted_at__isnull=True,
    )

    product = item.product

    # Handle company data
    company = None
    company_id = None
    if product.company and not product.company.deleted_at:
        company_id = product.company.id
        company = CompanyOut(
            id=product.company.id,
            name=product.company.name,
            title=product.company.title,
            slug=product.company.slug,
        )

    # Handle category data
    category = None
    category_id = None
    if product.category and not product.category.deleted_at:
        category_id = product.category.id
        category = CategoryOut(
            id=product.category.id,
            name=product.category.name,
            title=product.category.title,
            slug=product.category.slug,
        )

    # Handle product image URL
    image_url = None
    if product.image and product.image.name:
        image_url = product.image.url

    # Create and return the item output
    return WholesalerItemOut(
        id=item.id,
        product_id=product.id,
        product_name=product.name,
        price=item.base_price,
        inventory_count=item.inventory_count,
        price_expiry=item.price_expiry,
        image_url=image_url,
        company_id=company_id,
        category_id=category_id,
        company=company,
        category=category,
        unit=product.unit,
        unit_count=int(product.unit_count),
    )
