from typing import List
from ninja import Schema
from django.shortcuts import get_object_or_404
from stores.models import Store
from products.models import Region


class RegionOut(Schema):
    id: int
    name: str
    type: str
    slug: str


class CustomUserOut(Schema):
    id: int
    username: str
    email: str
    first_name: str
    last_name: str
    phone: str
    phone_verified: bool


class StoreIn(Schema):
    name: str
    description: str
    address: str
    city_id: int
    state_id: int
    country_id: int


class StoreOut(Schema):
    id: int
    owner: CustomUserOut
    name: str
    description: str
    address: str
    city: RegionOut
    state: RegionOut
    country: RegionOut
    created_at: str
    updated_at: str


class PaginatedStoreResponse(Schema):
    total: int
    items: List[StoreOut]


# GET: /api/v2/stores/me
def get_user_stores(request) -> List[StoreOut]:
    """
    Get all stores for the authenticated user.
    """
    stores = Store.objects.filter(owner=request.auth, deleted_at__isnull=True)
    return [
        StoreOut(
            id=store.id,
            owner=CustomUserOut(
                id=store.owner.id,
                username=store.owner.username,
                email=store.owner.email,
                first_name=store.owner.first_name,
                last_name=store.owner.last_name,
                phone=store.owner.phone,
                phone_verified=store.owner.phone_verified,
            ),
            name=store.name,
            description=store.description,
            address=store.address,
            city=RegionOut(
                id=store.city.id,
                name=store.city.name,
                type=store.city.type,
                slug=store.city.slug,
            ),
            state=RegionOut(
                id=store.state.id,
                name=store.state.name,
                type=store.state.type,
                slug=store.state.slug,
            ),
            country=RegionOut(
                id=store.country.id,
                name=store.country.name,
                type=store.country.type,
                slug=store.country.slug,
            ),
            created_at=store.created_at.isoformat(),
            updated_at=store.updated_at.isoformat(),
        )
        for store in stores
    ]


# GET: /api/v2/stores/{store_id}
def get_store_by_id(request, store_id: int) -> StoreOut:
    """
    Get a single store by its ID for the authenticated user.
    """
    store = get_object_or_404(
        Store, id=store_id, owner=request.auth, deleted_at__isnull=True
    )
    return StoreOut(
        id=store.id,
        owner=CustomUserOut(
            id=store.owner.id,
            username=store.owner.username,
            email=store.owner.email,
            first_name=store.owner.first_name,
            last_name=store.owner.last_name,
            phone=store.owner.phone,
            phone_verified=store.owner.phone_verified,
        ),
        name=store.name,
        description=store.description,
        address=store.address,
        city=RegionOut(
            id=store.city.id,
            name=store.city.name,
            type=store.city.type,
            slug=store.city.slug,
        ),
        state=RegionOut(
            id=store.state.id,
            name=store.state.name,
            type=store.state.type,
            slug=store.state.slug,
        ),
        country=RegionOut(
            id=store.country.id,
            name=store.country.name,
            type=store.country.type,
            slug=store.country.slug,
        ),
        created_at=store.created_at.isoformat(),
        updated_at=store.updated_at.isoformat(),
    )


# POST: /api/v2/stores/
def create_store(request, store_in: StoreIn) -> StoreOut:
    """
    Create a new store for the authenticated user.
    """
    city = get_object_or_404(Region, id=store_in.city_id, deleted_at__isnull=True)
    state = get_object_or_404(Region, id=store_in.state_id, deleted_at__isnull=True)
    country = get_object_or_404(Region, id=store_in.country_id, deleted_at__isnull=True)

    store = Store.objects.create(
        owner=request.auth,
        name=store_in.name,
        description=store_in.description,
        address=store_in.address,
        city=city,
        state=state,
        country=country,
    )
    return StoreOut(
        id=store.id,
        owner=CustomUserOut(
            id=store.owner.id,
            username=store.owner.username,
            email=store.owner.email,
            first_name=store.owner.first_name,
            last_name=store.owner.last_name,
            phone=store.owner.phone,
            phone_verified=store.owner.phone_verified,
        ),
        name=store.name,
        description=store.description,
        address=store.address,
        city=RegionOut(
            id=store.city.id,
            name=store.city.name,
            type=store.city.type,
            slug=store.city.slug,
        ),
        state=RegionOut(
            id=store.state.id,
            name=store.state.name,
            type=store.state.type,
            slug=store.state.slug,
        ),
        country=RegionOut(
            id=store.country.id,
            name=store.country.name,
            type=store.country.type,
            slug=store.country.slug,
        ),
        created_at=store.created_at.isoformat(),
        updated_at=store.updated_at.isoformat(),
    )


# PUT: /api/v2/stores/{store_id}
def update_store(request, store_id: int, store_in: StoreIn) -> StoreOut:
    """
    Update an existing store for the authenticated user.
    """
    store = get_object_or_404(
        Store, id=store_id, owner=request.auth, deleted_at__isnull=True
    )

    city = get_object_or_404(Region, id=store_in.city_id, deleted_at__isnull=True)
    state = get_object_or_404(Region, id=store_in.state_id, deleted_at__isnull=True)
    country = get_object_or_404(Region, id=store_in.country_id, deleted_at__isnull=True)

    store.name = store_in.name
    store.description = store_in.description
    store.address = store_in.address
    store.city = city
    store.state = state
    store.country = country
    store.save()
    return StoreOut(
        id=store.id,
        owner=CustomUserOut(
            id=store.owner.id,
            username=store.owner.username,
            email=store.owner.email,
            first_name=store.owner.first_name,
            last_name=store.owner.last_name,
            phone=store.owner.phone,
            phone_verified=store.owner.phone_verified,
        ),
        name=store.name,
        description=store.description,
        address=store.address,
        city=RegionOut(
            id=store.city.id,
            name=store.city.name,
            type=store.city.type,
            slug=store.city.slug,
        ),
        state=RegionOut(
            id=store.state.id,
            name=store.state.name,
            type=store.state.type,
            slug=store.state.slug,
        ),
        country=RegionOut(
            id=store.country.id,
            name=store.country.name,
            type=store.country.type,
            slug=store.country.slug,
        ),
        created_at=store.created_at.isoformat(),
        updated_at=store.updated_at.isoformat(),
    )


def delete_store(request, store_id: int):
    """
    Delete a store for the authenticated user.
    """
    store = get_object_or_404(
        Store, id=store_id, owner=request.auth, deleted_at__isnull=True
    )
    store.delete()
    return {"success": True}
