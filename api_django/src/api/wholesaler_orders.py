# POST: /api/v2/wholesaler-orders/accept/{order_id}
from datetime import datetime
from typing import Optional

from ninja import Schema, Field
from django.shortcuts import get_object_or_404
from django.db import transaction as db_transaction
from django.utils import timezone
from accounts.models import CustomUser, Wallet, Transaction, TransactionActionEnum
from accounts.utils import send_notification
from api.stores import CustomUserOut, RegionOut, StoreOut
from stores.models import Order, OrderStatus
from wholesalers.models import Wholesaler


def add_fees_to_wholesaler_wallet(
    user: CustomUser, fee: float, action: TransactionActionEnum
):
    # create wallet if not exists
    if not Wallet.objects.filter(user=user).exists():
        Wallet.objects.create(user=user, balance=0)
    wholesaler_wallet = Wallet.objects.get(user=user)
    wholesaler_wallet.balance -= fee
    wholesaler_wallet.save()
    Transaction.objects.create(
        user=user,
        wallet=wholesaler_wallet,
        action=action,
        amount=fee,
    )


class WholesalerOrderOut(Schema):
    id: int
    store: StoreOut
    total_price: float
    fees: float
    deliver_at: datetime
    products_total_price: float
    products_total_quantity: int
    status: OrderStatus
    status_reason: Optional[str] = None
    status_updated_at: datetime
    status_updated_by: Optional[CustomUserOut] = None
    completed_at: Optional[datetime] = None
    final_completed_price: Optional[float] = None


class WholesalerOrdersListOut(Schema):
    orders: list[WholesalerOrderOut]


def list_my_wholesaler_orders(
    request, offset: int = 0, limit: int = 10
) -> WholesalerOrdersListOut:
    wholesaler = get_object_or_404(
        Wholesaler, user=request.auth, deleted_at__isnull=True
    )
    orders = (
        Order.objects.filter(wholesaler=wholesaler, deleted_at__isnull=True)
        .prefetch_related("store__city", "store__state", "store__country")
        .order_by("-created_at")[offset : offset + limit]
    )
    return WholesalerOrdersListOut(
        orders=[
            WholesalerOrderOut(
                id=order.id,
                store=StoreOut(
                    id=order.store.id,
                    name=order.store.name,
                    description=order.store.description,
                    address=order.store.address,
                    city=RegionOut(
                        id=order.store.city.id,
                        name=order.store.city.name,
                        type=order.store.city.type,
                        slug=order.store.city.slug,
                    ),
                    state=RegionOut(
                        id=order.store.state.id,
                        name=order.store.state.name,
                        type=order.store.state.type,
                        slug=order.store.state.slug,
                    ),
                    country=RegionOut(
                        id=order.store.country.id,
                        name=order.store.country.name,
                        type=order.store.country.type,
                        slug=order.store.country.slug,
                    ),
                    owner=CustomUserOut(
                        id=order.store.owner.id,
                        username=order.store.owner.username,
                        email=order.store.owner.email,
                        first_name=order.store.owner.first_name,
                        last_name=order.store.owner.last_name,
                        phone=order.store.owner.phone,
                        phone_verified=order.store.owner.phone_verified,
                    ),
                    created_at=order.store.created_at.isoformat(),
                    updated_at=order.store.updated_at.isoformat(),
                ),
                total_price=order.total_price,
                fees=order.fees,
                deliver_at=order.deliver_at,
                products_total_price=order.products_total_price,
                products_total_quantity=order.products_total_quantity,
                status=order.status,
                status_reason=order.status_reason,
                status_updated_at=order.status_updated_at,
                status_updated_by=order.status_updated_by,
                completed_at=order.completed_at,
                final_completed_price=order.final_completed_price,
            )
            for order in orders
        ]
    )


# POST: /api/v2/wholesaler-orders/accept/{order_id}
def accept_order(request, order_id: int):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order, id=order_id, wholesaler__user=request.auth, deleted_at__isnull=True
        )
        if order.status != OrderStatus.PENDING:
            return {"error": "Order is not pending."}
        order.status = OrderStatus.PROCESSING
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.save()
        # Notify reseller
        try:
            send_notification(
                order.store.owner.id,
                "تم قبول الطلب",
                f"تم قبول طلبك رقم {order.id} من قبل المورد.",
            )
        except Exception:
            pass
        return {"success": True}


# POST: /api/v2/wholesaler-orders/reject/{order_id}
def reject_order(request, order_id: int):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order, id=order_id, wholesaler__user=request.auth, deleted_at__isnull=True
        )
        if order.status not in [OrderStatus.PENDING, OrderStatus.PROCESSING]:
            return {"error": "Order cannot be rejected in its current status."}
        order.status = OrderStatus.CANCELLED
        order.status_reason = "Rejected by wholesaler"
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.completed_at = timezone.now()
        order.save()
        # Deduct fees from wholesaler wallet
        add_fees_to_wholesaler_wallet(
            request.auth,
            order.fees * -1,
            TransactionActionEnum.ORDER_REJECT_FEES,
        )
        # Notify reseller
        try:
            send_notification(
                order.store.owner.id,
                "تم رفض الطلب",
                f"تم رفض طلبك رقم {order.id} من قبل المورد.",
            )
        except Exception:
            pass
        return {"success": True}


# POST: /api/v2/wholesaler-orders/cancel/{order_id}
def cancel_order(request, order_id: int):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order, id=order_id, wholesaler__user=request.auth, deleted_at__isnull=True
        )
        if order.status not in [OrderStatus.PENDING, OrderStatus.PROCESSING]:
            return {"error": "Order cannot be cancelled in its current status."}
        order.status = OrderStatus.CANCELLED
        order.status_reason = "Cancelled by wholesaler"
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.completed_at = timezone.now()
        order.save()
        # Deduct fees from wholesaler wallet
        add_fees_to_wholesaler_wallet(
            request.auth,
            order.fees * -1,
            TransactionActionEnum.ORDER_CANCEL_FEES,
        )
        # Notify reseller
        try:
            send_notification(
                order.store.owner.id,
                "تم إلغاء الطلب",
                f"تم إلغاء طلبك رقم {order.id} من قبل المورد.",
            )
        except Exception:
            pass
        return {"success": True}


class OrderCompleteIn(Schema):
    order_id: int = Field(..., description="Order ID")
    final_completed_price: Optional[float] = Field(
        ..., description="Final completed price of the order"
    )


# POST: /api/v2/wholesaler-orders/complete
def complete_order(request, order_in: OrderCompleteIn):
    with db_transaction.atomic():
        order = get_object_or_404(
            Order,
            id=order_in.order_id,
            wholesaler__user=request.auth,
            deleted_at__isnull=True,
        )
        if order.status not in [OrderStatus.PROCESSING]:
            return {"error": "Order cannot be completed in its current status."}
        order.status = OrderStatus.DELIVERED
        order.final_completed_price = (
            order_in.final_completed_price or order.total_price
        )
        order.completed_at = timezone.now()
        order.status_updated_by = request.auth
        order.status_updated_at = timezone.now()
        order.save()
        # Deduct fees from wholesaler wallet
        add_fees_to_wholesaler_wallet(
            request.auth,
            order.fees * -1,
            TransactionActionEnum.ORDER_COMPLETE_FEES,
        )
        # Notify reseller
        try:
            send_notification(
                order.store.owner.id,
                "تم اكتمال الطلب",
                f"تم اكتمال طلبك رقم {order.id} من قبل المورد.",
            )
        except Exception:
            pass
        return {"success": True}
