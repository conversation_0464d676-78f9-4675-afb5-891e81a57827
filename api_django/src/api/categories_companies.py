from ninja import Schema
from products.models import Category, Company


class CategoryCompanyItemOut(Schema):
    id: int
    name: str


class CategoryCompanyOut(Schema):
    categories: list[CategoryCompanyItemOut]
    companies: list[CategoryCompanyItemOut]


# GET: /api/v2/categories-companies/
def list_categories_companies(request) -> CategoryCompanyOut:
    categories = Category.objects.filter(deleted_at__isnull=True)
    companies = Company.objects.filter(deleted_at__isnull=True)
    return CategoryCompanyOut(
        categories=[
            CategoryCompanyItemOut(id=category.id, name=category.name)
            for category in categories
        ],
        companies=[
            CategoryCompanyItemOut(id=company.id, name=company.name)
            for company in companies
        ],
    )
