from ninja import Schema
from typing import Optional
from datetime import datetime
from decimal import Decimal


# Region schemas (simplified versions of the models in products app)
class RegionOut(Schema):
    id: int
    name: str
    type: str
    code: Optional[str] = None


# Product schemas (simplified for reference)
class ProductOut(Schema):
    id: int
    name: str
    barcode: str
    image_url: Optional[str] = None


# Store schemas
class WholesalerIn(Schema):
    category: str
    title: str
    username: str


# Store update schema
class WholesalerUpdate(Schema):
    category: Optional[str] = None
    title: Optional[str] = None
    username: Optional[str] = None


# Store output schema
class WholesalerOut(Schema):
    id: int
    category: str
    title: str
    username: str
    logo_url: Optional[str] = None
    background_image_url: Optional[str] = None


# Region min charge schemas
class RegionMinChargeIn(Schema):
    region_id: int
    min_charge: Decimal
    min_items: int


class RegionMinChargeOut(Schema):
    id: int
    region: RegionOut
    min_charge: Decimal
    min_items: int


# Item (product pricing and inventory) schemas
class ItemIn(Schema):
    product_id: int
    base_price: Decimal
    inventory_count: int = 0
    price_expiry: Optional[datetime] = None


class ItemUpdate(Schema):
    base_price: Optional[Decimal] = None
    inventory_count: Optional[int] = None
    price_expiry: Optional[datetime] = None


class ItemOut(Schema):
    id: int
    product: ProductOut
    base_price: Decimal
    inventory_count: int
    price_expiry: datetime
    created_at: datetime


# Inventory transaction schemas
class InventoryTransactionIn(Schema):
    transaction_type: str  # "ADDITION" or "SUBTRACTION"
    quantity: int
    notes: Optional[str] = None


class InventoryTransactionOut(Schema):
    id: int
    transaction_type: str
    quantity: int
    notes: Optional[str] = None
    created_at: datetime


class InventoryUpdateResponse(Schema):
    success: bool
    message: str
    new_inventory_count: int
