# Generated by Django 5.1.7 on 2025-03-26 09:03

import django.db.models.deletion
import wholesalers.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Wholesaler',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(choices=[('PHARMACEUTICAL', 'Pharmaceutical'), ('GROCERY', 'Grocery'), ('ELECTRONICS', 'Electronics')], default='GROCERY', max_length=255)),
                ('title', models.CharField(db_index=True, max_length=255)),
                ('username', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('background_image', models.ImageField(upload_to='wholesalers/backgrounds/')),
                ('logo', models.ImageField(upload_to='wholesalers/logos/')),
                ('min_charge', models.DecimalField(decimal_places=2, default=-1, help_text='Minimum charge for delivery', max_digits=10)),
                ('min_items', models.IntegerField(help_text='Minimum number of items to be ordered')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wholesalers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Wholesaler',
                'verbose_name_plural': 'Wholesalers',
            },
        ),
        migrations.CreateModel(
            name='Item',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_expiry', models.DateTimeField(default=wholesalers.models.default_price_expiry)),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity_unit', models.CharField(max_length=255)),
                ('expires_at', models.DateTimeField(default=wholesalers.models.default_price_expiry)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wholesalers', to='products.product')),
                ('wholesaler', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='wholesalers.wholesaler')),
            ],
            options={
                'verbose_name': 'Item',
                'verbose_name_plural': 'Items',
            },
        ),
        migrations.CreateModel(
            name='ItemRegionPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('price_expiry', models.DateTimeField(default=wholesalers.models.default_price_expiry)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='region_prices', to='wholesalers.item')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_prices', to='products.region')),
            ],
            options={
                'verbose_name': 'Region Price',
                'verbose_name_plural': 'Region Prices',
                'indexes': [models.Index(fields=['item', 'region'], name='wholesalers_item_id_530dda_idx'), models.Index(fields=['region', 'price'], name='wholesalers_region__7dc025_idx')],
                'unique_together': {('item', 'region')},
            },
        ),
        migrations.AddIndex(
            model_name='item',
            index=models.Index(fields=['wholesaler', 'product'], name='wholesalers_wholesa_dda3f7_idx'),
        ),
    ]
