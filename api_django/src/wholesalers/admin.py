from django.contrib import admin
from .models import RegionMinCharge, Wholesaler, Item, InventoryTransaction


class RegionMinChargeInline(admin.TabularInline):
    model = RegionMinCharge
    extra = 1
    raw_id_fields = ("region",)
    autocomplete_fields = ["region"]


class WholesalerAdmin(admin.ModelAdmin):
    list_display = (
        "title",
        "username",
        "category",
        "user",
        "created_at",
    )
    list_filter = ("category", "created_at")
    search_fields = ("title", "username", "user__username", "user__email")
    raw_id_fields = ("user",)
    readonly_fields = ("created_at", "updated_at")
    inlines = [RegionMinChargeInline]
    fieldsets = (
        (None, {"fields": ("title", "username", "user", "category")}),
        (
            "Media",
            {
                "fields": ("logo", "background_image"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


class InventoryTransactionInline(admin.TabularInline):
    model = InventoryTransaction
    extra = 1
    readonly_fields = ("created_at",)
    fields = ("transaction_type", "quantity", "notes", "created_at")


class ItemAdmin(admin.ModelAdmin):
    list_display = (
        "product",
        "wholesaler",
        "base_price",
        "inventory_count",
        "price_expiry",
    )
    list_filter = (
        "wholesaler",
        "price_expiry",
        "created_at",
    )
    search_fields = (
        "product__name",
        "product__barcode",
        "wholesaler__title",
        "wholesaler__username",
    )
    raw_id_fields = ("wholesaler", "product")
    autocomplete_fields = ["product"]
    readonly_fields = ("created_at", "updated_at")
    inlines = [InventoryTransactionInline]
    fieldsets = (
        (None, {"fields": ("wholesaler", "product")}),
        (
            "Pricing & Inventory",
            {
                "fields": ("base_price", "inventory_count"),
            },
        ),
        (
            "Dates",
            {
                "fields": ("price_expiry", "expires_at"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


class InventoryTransactionAdmin(admin.ModelAdmin):
    list_display = ("item", "transaction_type", "quantity", "created_at")
    list_filter = ("transaction_type", "created_at")
    search_fields = (
        "item__product__name",
        "item__product__barcode",
        "item__wholesaler__title",
        "notes",
    )
    raw_id_fields = ("item",)
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("item", "transaction_type", "quantity", "notes")}),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


# Register all models with the admin site
admin.site.register(Wholesaler, WholesalerAdmin)
admin.site.register(Item, ItemAdmin)
admin.site.register(InventoryTransaction, InventoryTransactionAdmin)
