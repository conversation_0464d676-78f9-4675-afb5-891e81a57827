"""
Signal handlers for the products app.

This module contains signal handlers that are triggered on various product-related events.
"""

import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from django.db import connection
import openai
from upstash_vector import Index

from .models import Product

# Configure logger
logger = logging.getLogger(__name__)

# Configure OpenAI API
openai.api_key = settings.OPENAI_API_KEY


def generate_embedding(text):
    """
    Generate an embedding for the given text using OpenAI's API.

    Args:
        text (str): The text to generate an embedding for.

    Returns:
        list: The embedding vector.
    """
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-large",
            input=text,
            dimensions=384,
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        return None


def get_product_metadata(product):
    """
    Get comprehensive metadata for a product including company and category IDs.

    Args:
        product: The Product instance

    Returns:
        dict: Dictionary containing all metadata for the product
    """
    metadata = {
        "product_id": product.id,
        "company_id": product.company.id if product.company else None,
        "category_id": product.category.id if product.category else None,
    }

    return metadata


def save_to_upstash(product, embedding):
    """
    Save the embedding to Upstash Vector DB with comprehensive metadata.

    Args:
        product: The Product instance.
        embedding (list): The embedding vector.

    Returns:
        bool: True if successful, False otherwise.
    """
    try:
        # Initialize Upstash Vector client
        index = Index(
            url=settings.UPSTASH_VECTOR_URL,
            token=settings.UPSTASH_VECTOR_TOKEN,
        )

        # Get comprehensive metadata
        metadata = get_product_metadata(product)

        # Upsert the embedding with enhanced metadata
        index.upsert(
            vectors=[(f"product_{product.id}", embedding, metadata)],
        )

        return True
    except Exception as e:
        logger.error(f"Error saving to Upstash Vector DB: {str(e)}")
        return False


@receiver(post_save, sender=Product)
def product_post_save(sender, instance, created, **kwargs):
    """
    Signal handler for when a product is saved.

    This handler is triggered after a product is saved. It:
    1. Updates the search vector for full-text search
    2. Generates an embedding for the product data and saves it to Upstash Vector DB

    Args:
        sender: The model class that sent the signal.
        instance: The actual instance being saved.
        created (bool): True if a new record was created.
        **kwargs: Additional keyword arguments.
    """
    try:
        # Get product data
        product_name = instance.name
        product_title = instance.title
        product_description = instance.description
        company_name = instance.company.name if instance.company else "Unknown Company"
        category_name = instance.category.name if instance.category else "Uncategorized"

        # 1. Update search vector for PostgreSQL full-text search
        # Check if we're using PostgreSQL
        is_postgres = (
            settings.DATABASES["default"]["ENGINE"] == "django.db.backends.postgresql"
        )

        if is_postgres:
            # Update the search vector using raw SQL for better control over weights and language
            with connection.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE products_product
                    SET search_vector =
                        setweight(to_tsvector('arabic', COALESCE(%s, '')), 'A') ||
                        setweight(to_tsvector('arabic', COALESCE(%s, '')), 'A') ||
                        setweight(to_tsvector('arabic', COALESCE(%s, '')), 'B') ||
                        setweight(to_tsvector('arabic', COALESCE(%s, '')), 'C') ||
                        setweight(to_tsvector('arabic', COALESCE(%s, '')), 'C')
                    WHERE id = %s
                    """,
                    [
                        product_name,
                        product_title,
                        product_description,
                        company_name,
                        category_name,
                        instance.id,
                    ],
                )
                logger.info(f"Updated search vector for product {instance.id}")

        # 2. Generate and save embedding to Upstash Vector DB (only for new products)
        if created:
            # Format text for embedding
            text = f"اسم المنتج: {product_name} | اسم الشركة: {company_name} | التصنيف: {category_name}"

            # Generate embedding
            embedding = generate_embedding(text)
            if not embedding:
                logger.error(f"Failed to generate embedding for product {instance.id}")
                return

            # Save to Upstash Vector DB
            success = save_to_upstash(instance, embedding)
            if success:
                logger.info(f"Successfully saved embedding for product {instance.id}")
            else:
                logger.error(f"Failed to save embedding for product {instance.id}")

    except Exception as e:
        logger.exception(f"Error in product_post_save signal: {str(e)}")
