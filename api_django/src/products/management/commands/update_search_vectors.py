import logging
import json
from django.core.management.base import BaseCommand
from django.db import transaction, connection
from django.conf import settings
from django.contrib.postgres.search import SearchVector
from products.models import Product

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Updates search vectors for all products"

    def add_arguments(self, parser):
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of products to process in each batch",
        )

    def handle(self, *args, **options):
        batch_size = options["batch_size"]

        # Get total count of products
        total_products = Product.objects.filter(deleted_at__isnull=True).count()
        self.stdout.write(f"Found {total_products} active products to process")

        # Process in batches to avoid memory issues
        processed = 0
        updated = 0

        # Get all products in batches
        while processed < total_products:
            # Get a batch of products
            products = Product.objects.filter(deleted_at__isnull=True).order_by("id")[
                processed : processed + batch_size
            ]

            with transaction.atomic():
                for product in products:
                    # Check if we're using PostgreSQL or SQLite
                    is_postgres = (
                        settings.DATABASES["default"]["ENGINE"]
                        == "django.db.backends.postgresql"
                    )

                    if is_postgres:
                        # PostgreSQL: Use native full-text search
                        with connection.cursor() as cursor:
                            cursor.execute(
                                """
                                UPDATE products_product
                                SET search_vector =
                                    setweight(to_tsvector('arabic', COALESCE(name, '')), 'A') ||
                                    setweight(to_tsvector('arabic', COALESCE(title, '')), 'A') ||
                                    setweight(to_tsvector('arabic', COALESCE(description, '')), 'B') ||
                                    setweight(to_tsvector('arabic', COALESCE(
                                        (SELECT name FROM products_company WHERE id = products_product.company_id), '')), 'C') ||
                                    setweight(to_tsvector('arabic', COALESCE(
                                        (SELECT name FROM products_category WHERE id = products_product.category_id), '')), 'C')
                                WHERE id = %s
                                """,
                                [product.id],
                            )
                            updated += cursor.rowcount
                    else:
                        # SQLite: Store a JSON representation for basic search
                        # Get product data
                        product_name = product.name
                        product_title = product.title
                        product_description = product.description
                        company_name = (
                            product.company.name
                            if product.company
                            else "Unknown Company"
                        )
                        category_name = (
                            product.category.name
                            if product.category
                            else "Uncategorized"
                        )

                        # Create search vector as JSON
                        search_vector = {
                            "name": product_name.lower(),
                            "title": product_title.lower(),
                            "description": product_description.lower()
                            if product_description
                            else "",
                            "company": company_name.lower(),
                            "category": category_name.lower(),
                            "tokens": list(
                                set(
                                    [token.lower() for token in product_name.split()]
                                    + [token.lower() for token in product_title.split()]
                                )
                            ),
                        }

                        # Convert to JSON string for SQLite
                        search_vector_json = json.dumps(search_vector)

                        with connection.cursor() as cursor:
                            # SQLite uses ? for parameters, PostgreSQL uses %s
                            # Django will convert %s to ? for SQLite
                            cursor.execute(
                                "UPDATE products_product SET search_vector = %s WHERE id = %s",
                                [search_vector_json, product.id],
                            )
                            updated += cursor.rowcount

            # Update progress
            processed += len(products)
            self.stdout.write(
                f"Processed {processed}/{total_products} products, updated {updated}"
            )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully updated search vectors for {updated} products"
            )
        )
