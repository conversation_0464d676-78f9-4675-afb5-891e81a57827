import logging
from typing import Dict, Set
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from upstash_vector import Index
from products.models import Product

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Update Upstash Vector Database metadata with company and category IDs"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Run in dry-run mode (no actual changes will be made)",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of products to process in each batch (default: 100)",
        )
        parser.add_argument(
            "--product-id",
            type=int,
            help="Update metadata for a specific product ID only",
        )

    def handle(self, *args, **options):
        # Validate required settings
        if not settings.UPSTASH_VECTOR_URL or not settings.UPSTASH_VECTOR_TOKEN:
            raise CommandError(
                "UPSTASH_VECTOR_URL and UPSTASH_VECTOR_TOKEN must be set in settings"
            )

        dry_run = options["dry_run"]
        batch_size = options["batch_size"]
        product_id = options.get("product_id")

        self.stdout.write("Starting Upstash Vector DB metadata update")
        self.stdout.write(f"Dry run mode: {dry_run}")
        self.stdout.write(f"Batch size: {batch_size}")

        if product_id:
            self.stdout.write(f"Processing single product ID: {product_id}")

        try:
            # Initialize the updater
            updater = VectorMetadataUpdater(dry_run=dry_run, stdout=self.stdout)

            if product_id:
                # Update single product
                updater.update_single_product(product_id)
            else:
                # Update all products
                updater.update_all_products(batch_size=batch_size)

            updater.print_summary()
            self.stdout.write(
                self.style.SUCCESS("Metadata update completed successfully")
            )

        except KeyboardInterrupt:
            self.stdout.write("Update interrupted by user")
            return
        except Exception as e:
            raise CommandError(f"Update failed with error: {str(e)}")


class VectorMetadataUpdater:
    """Class to handle updating vector metadata in Upstash Vector DB."""

    def __init__(self, dry_run: bool = False, stdout=None):
        """
        Initialize the updater.

        Args:
            dry_run: If True, only log what would be done without making changes
            stdout: Django command stdout for output
        """
        self.dry_run = dry_run
        self.stdout = stdout
        self.updated_count = 0
        self.error_count = 0
        self.skipped_count = 0

        # Initialize Upstash Vector client
        try:
            self.index = Index(
                url=settings.UPSTASH_VECTOR_URL,
                token=settings.UPSTASH_VECTOR_TOKEN,
            )
            self._log_info("Successfully connected to Upstash Vector DB")
        except Exception as e:
            self._log_error(f"Failed to connect to Upstash Vector DB: {str(e)}")
            raise

    def _log_info(self, message):
        """Log info message to both logger and stdout."""
        logger.info(message)
        if self.stdout:
            self.stdout.write(message)

    def _log_error(self, message):
        """Log error message to both logger and stdout."""
        logger.error(message)
        if self.stdout:
            self.stdout.write(self.stdout.style.ERROR(message))

    def _log_warning(self, message):
        """Log warning message to both logger and stdout."""
        logger.warning(message)
        if self.stdout:
            self.stdout.write(self.stdout.style.WARNING(message))

    def get_product_metadata(self, product: Product) -> Dict:
        """
        Get metadata for a product including company and category IDs.

        Args:
            product: The Product instance

        Returns:
            Dictionary containing all metadata for the product
        """
        metadata = {
            "product_id": product.id,
            "company_id": product.company.id if product.company else None,
            "category_id": product.category.id if product.category else None,
        }

        return metadata

    def update_vector_metadata(self, product_id: int, metadata: Dict) -> bool:
        """
        Update metadata for a specific product vector.

        Args:
            product_id: The product ID
            metadata: The new metadata to set

        Returns:
            True if successful, False otherwise
        """
        try:
            vector_id = f"product_{product_id}"

            if self.dry_run:
                self._log_info(
                    f"[DRY RUN] Would update vector {vector_id} with metadata: {metadata}"
                )
                return True

            # Fetch the existing vector to get its embedding
            existing_vector = self.index.fetch([vector_id])

            if not existing_vector or len(existing_vector) == 0:
                self._log_warning(f"Vector {vector_id} not found in database, skipping")
                return False

            # Get the embedding from the existing vector
            vector_data = existing_vector[0]
            embedding = vector_data.values

            # Update the vector with new metadata
            self.index.upsert(vectors=[(vector_id, embedding, metadata)])

            self._log_info(f"Successfully updated vector {vector_id}")
            return True

        except Exception as e:
            self._log_error(f"Error updating vector for product {product_id}: {str(e)}")
            return False

    def update_single_product(self, product_id: int) -> None:
        """
        Update metadata for a single product.

        Args:
            product_id: The ID of the product to update
        """
        try:
            product = Product.objects.select_related("company", "category").get(
                id=product_id, deleted_at__isnull=True
            )

            self._log_info(f"Processing product {product_id}: {product.name}")

            # Get metadata for this product
            metadata = self.get_product_metadata(product)

            # Update the vector metadata
            if self.update_vector_metadata(product.id, metadata):
                self.updated_count += 1
            else:
                self.skipped_count += 1

        except Product.DoesNotExist:
            self._log_error(f"Product {product_id} not found or is deleted")
            self.error_count += 1
        except Exception as e:
            self._log_error(f"Error processing product {product_id}: {str(e)}")
            self.error_count += 1

    def update_all_products(self, batch_size: int = 100) -> None:
        """
        Update metadata for all products in batches.

        Args:
            batch_size: Number of products to process in each batch
        """
        # Get all active products
        products = (
            Product.objects.filter(deleted_at__isnull=True)
            .select_related("company", "category")
            .order_by("id")
        )

        total_products = products.count()
        self._log_info(f"Found {total_products} active products to process")

        if self.dry_run:
            self._log_info("DRY RUN MODE: No actual changes will be made")

        # Process in batches
        for i in range(0, total_products, batch_size):
            batch_products = products[i : i + batch_size]
            self._log_info(
                f"Processing batch {i // batch_size + 1}: products {i + 1} to {min(i + batch_size, total_products)}"
            )

            for product in batch_products:
                try:
                    # Get metadata for this product
                    metadata = self.get_product_metadata(product)

                    # Update the vector metadata
                    if self.update_vector_metadata(product.id, metadata):
                        self.updated_count += 1
                    else:
                        self.skipped_count += 1

                except Exception as e:
                    self._log_error(f"Error processing product {product.id}: {str(e)}")
                    self.error_count += 1

            # Log progress
            self._log_info(
                f"Batch complete. Updated: {self.updated_count}, Skipped: {self.skipped_count}, Errors: {self.error_count}"
            )

    def print_summary(self) -> None:
        """Print a summary of the update operation."""
        if self.stdout:
            self.stdout.write("=" * 50)
            self.stdout.write("UPDATE SUMMARY")
            self.stdout.write("=" * 50)
            self.stdout.write(f"Successfully updated: {self.updated_count}")
            self.stdout.write(f"Skipped (not found): {self.skipped_count}")
            self.stdout.write(f"Errors: {self.error_count}")
            self.stdout.write(
                f"Total processed: {self.updated_count + self.skipped_count + self.error_count}"
            )

            if self.dry_run:
                self.stdout.write(
                    self.stdout.style.WARNING(
                        "DRY RUN MODE - No actual changes were made"
                    )
                )

        # Also log to logger
        logger.info("=" * 50)
        logger.info("UPDATE SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Successfully updated: {self.updated_count}")
        logger.info(f"Skipped (not found): {self.skipped_count}")
        logger.info(f"Errors: {self.error_count}")
        logger.info(
            f"Total processed: {self.updated_count + self.skipped_count + self.error_count}"
        )

        if self.dry_run:
            logger.info("DRY RUN MODE - No actual changes were made")
