"""
New API endpoints for enhanced product listing with pagination and filtering.
"""

from typing import List, Dict, Any, Optional
from django.core.paginator import Paginator
from django.core.cache import cache
from django.db.models import Q
from pydantic import BaseModel

from .models import Product
from .schemas import ProductWithPricingOut
from .home_views import _get_region_hierarchy, _enrich_product_with_pricing
from .views import _product_to_schema
from wholesalers.models import Wholesaler, Item
from wholesalers.schemas import WholesalerOut as WholesalerOutSchema
from django.conf import settings
from core.custom_router import CustomRouter

router = CustomRouter(tags=["products-v2"])


class PaginatedProductResponse(BaseModel):
    """Response model for paginated products"""

    products: List[ProductWithPricingOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


def _get_filtered_products(
    region_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    company_id: Optional[int] = None,
):
    """
    Get filtered products based on various criteria.

    Args:
        region_id: Filter by region (includes parent regions)
        wholesaler_id: Filter by specific wholesaler
        search: Search in product name, title, description
        category_id: Filter by category
        company_id: Filter by company
    """
    # Start with base queryset
    queryset = Product.objects.filter(deleted_at__isnull=True)

    # Apply basic filters
    if category_id is not None:
        queryset = queryset.filter(
            category_id=category_id, category__deleted_at__isnull=True
        )

    if company_id is not None:
        queryset = queryset.filter(
            company_id=company_id, company__deleted_at__isnull=True
        )

    # Apply search filter
    if search:
        search_query = (
            Q(name__icontains=search)
            | Q(title__icontains=search)
            | Q(description__icontains=search)
        )
        if hasattr(Product, "search_vector") and Product.search_vector:
            # Use PostgreSQL full-text search if available
            from django.contrib.postgres.search import SearchQuery

            search_query |= Q(search_vector=SearchQuery(search))
        queryset = queryset.filter(search_query)

    # Filter by products that have active wholesaler items
    queryset = queryset.filter(
        wholesalers__deleted_at__isnull=True,
        wholesalers__inventory_count__gt=0,
    )

    # Apply wholesaler filter
    if wholesaler_id is not None:
        queryset = queryset.filter(
            wholesalers__wholesaler_id=wholesaler_id,
            wholesalers__wholesaler__deleted_at__isnull=True,
        )

    # Apply region filter
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        if region_ids:
            queryset = queryset.filter(
                wholesalers__wholesaler__region_min_charge__region_id__in=region_ids,
                wholesalers__wholesaler__region_min_charge__deleted_at__isnull=True,
            )

    return queryset.distinct().order_by("-created_at")


def _get_wholesaler_specific_pricing(
    product_id: int, wholesaler_id: int
) -> Dict[str, Any]:
    """
    Helper function to get pricing information for a product from a specific wholesaler only.
    Returns a dictionary with the price from the specified wholesaler.
    """
    # Get the item for this product from the specific wholesaler
    try:
        item = Item.objects.select_related("wholesaler").get(
            product_id=product_id,
            wholesaler_id=wholesaler_id,
            deleted_at__isnull=True,
            inventory_count__gt=0,
            wholesaler__deleted_at__isnull=True,
        )
    except Item.DoesNotExist:
        return {
            "lowest_price": None,
            "lowest_price_wholesaler": None,
            "other_prices": [],
            "price_range": None,
        }

    # Convert wholesaler to schema
    wholesaler_schema = WholesalerOutSchema(
        id=item.wholesaler.id,
        category=item.wholesaler.category,
        title=item.wholesaler.title,
        username=item.wholesaler.username,
        logo_url=item.wholesaler.logo.url if item.wholesaler.logo else None,
        background_image_url=item.wholesaler.background_image.url
        if item.wholesaler.background_image
        else None,
        created_at=item.wholesaler.created_at,
    )

    # Return pricing information with only this wholesaler's price
    return {
        "lowest_price": item.base_price,
        "lowest_price_wholesaler": wholesaler_schema,
        "other_prices": [],  # Empty since we only want this wholesaler's price
        "price_range": {"min": item.base_price, "max": item.base_price},
    }


def _enrich_product_with_wholesaler_pricing(
    product_data: dict, wholesaler_id: int
) -> dict:
    """
    Helper function to add pricing information from a specific wholesaler to a product dictionary.
    """
    pricing = _get_wholesaler_specific_pricing(product_data["id"], wholesaler_id)

    # Return the enriched product data
    return {
        **product_data,
        "lowest_price": pricing["lowest_price"],
        "lowest_price_wholesaler": pricing["lowest_price_wholesaler"],
        "other_prices": pricing["other_prices"],
        "price_range": pricing["price_range"],
    }


@router.get("/products", response=PaginatedProductResponse)
def get_products(
    request,
    page: int = 1,
    page_size: int = 20,
    region_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    company_id: Optional[int] = None,
):
    """
    Get products with pagination and filtering support.

    Query Parameters:
    - page: Page number (default: 1)
    - page_size: Number of items per page (default: 20, max: 100)
    - region_id: Filter by region ID (includes parent regions)
    - wholesaler_id: Filter by specific wholesaler ID
    - search: Search term for product name/title/description
    - category_id: Filter by category ID
    - company_id: Filter by company ID

    Example URLs:
    - /api/products?page=1&page_size=10
    - /api/products?region_id=3&wholesaler_id=5
    - /api/products?search=apple&category_id=2

    Note: When wholesaler_id is provided, only that wholesaler's pricing will be returned.
    """
    # Validate and set defaults
    page = max(1, page or 1)
    page_size = min(100, max(1, page_size or 20))  # Limit max page size to 100

    # Create cache key based on all parameters
    cache_key = f"products_list_{region_id}_{wholesaler_id}_{search}_{category_id}_{company_id}_{page}_{page_size}"

    if not settings.DEBUG:
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

    # Get filtered products
    products_queryset = _get_filtered_products(
        region_id=region_id,
        wholesaler_id=wholesaler_id,
        search=search,
        category_id=category_id,
        company_id=company_id,
    )

    # Apply pagination
    paginator = Paginator(products_queryset, page_size)
    page_obj = paginator.get_page(page)

    # Convert products to schema and enrich with pricing
    enriched_products = []
    for product in page_obj.object_list:
        product_data = _product_to_schema(product)

        # Use wholesaler-specific pricing if wholesaler_id is provided
        if wholesaler_id is not None:
            enriched_product = _enrich_product_with_wholesaler_pricing(
                product_data, wholesaler_id
            )
        else:
            enriched_product = _enrich_product_with_pricing(product_data)

        # Only include products that have at least one price
        if enriched_product["lowest_price"] is not None:
            enriched_products.append(ProductWithPricingOut(**enriched_product))

    # Prepare response
    result = PaginatedProductResponse(
        products=enriched_products,
        total_count=paginator.count,
        page=page,
        page_size=page_size,
        total_pages=paginator.num_pages,
        has_next=page_obj.has_next(),
        has_previous=page_obj.has_previous(),
    )

    # Cache for 30 minutes
    if not settings.DEBUG:
        cache.set(cache_key, result, 60 * 30)

    return result


@router.get("/products/wholesalers", response=List[Dict[str, Any]])
def get_wholesalers_for_region(request, region_id: Optional[int] = None):
    """
    Get available wholesalers for a specific region.
    Useful for populating wholesaler filter dropdown.

    Args:
        region_id: Optional region ID to filter wholesalers
    """
    cache_key = f"wholesalers_for_region_{region_id}"

    if not settings.DEBUG:
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

    # Base queryset for active wholesalers
    queryset = Wholesaler.objects.filter(deleted_at__isnull=True)

    # Filter by region if provided
    if region_id is not None:
        region_ids = _get_region_hierarchy(region_id)
        if region_ids:
            queryset = queryset.filter(
                region_min_charge__region_id__in=region_ids,
                region_min_charge__deleted_at__isnull=True,
            )

    # Only include wholesalers that have products with inventory
    queryset = queryset.filter(
        items__inventory_count__gt=0,
        items__deleted_at__isnull=True,
    ).distinct()

    # Convert to simple format
    result = [
        {
            "id": wholesaler.id,
            "title": wholesaler.title,
            "username": wholesaler.username,
            "category": wholesaler.category,
            "logo_url": wholesaler.logo.url if wholesaler.logo else None,
        }
        for wholesaler in queryset.order_by("title")
    ]

    # Cache for 1 hour
    if not settings.DEBUG:
        cache.set(cache_key, result, 60 * 60)

    return result
