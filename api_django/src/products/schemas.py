from ninja import Schema, <PERSON>
from typing import List, Optional, Generic, TypeVar, Dict
from decimal import Decimal
from datetime import datetime
from .models import MeasurementUnit
from wholesalers.schemas import WholesalerOut as WholesalerOutSchema


class CompanyIn(Schema):
    name: str
    title: str
    slug: str


class CompanyOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class CategoryIn(Schema):
    name: str
    title: str
    slug: str


class CategoryOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class ProductIn(Schema):
    name: str
    title: str
    barcode: str
    slug: str
    description: str = ""
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    image: str = None
    unit: str = MeasurementUnit.PIECE
    unit_count: Decimal = Decimal("1.0")


class ProductUpdate(Schema):
    name: Optional[str] = None
    title: Optional[str] = None
    barcode: Optional[str] = None
    slug: Optional[str] = None
    description: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    unit: Optional[str] = None
    unit_count: Optional[Decimal] = None


class ProductPriceInfo(Schema):
    """Schema for product price information from a specific wholesaler"""

    price: Decimal = Field(
        ..., description="The price of the product from this wholesaler"
    )
    wholesaler: WholesalerOutSchema = Field(
        ..., description="The wholesaler offering this price"
    )
    inventory_count: int = Field(
        ..., description="Current inventory count at this wholesaler"
    )
    price_expiry: Optional[datetime] = Field(
        None, description="When this price expires"
    )


class ProductOut(Schema):
    """Basic product information schema"""

    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: float


class ProductWithPricingOut(ProductOut):
    """Extended product information with pricing from wholesalers"""

    lowest_price: Optional[Decimal] = Field(
        None, description="The lowest available price from any wholesaler"
    )
    lowest_price_wholesaler: Optional[WholesalerOutSchema] = Field(
        None, description="The wholesaler offering the lowest price"
    )
    other_prices: List[ProductPriceInfo] = Field(
        default_factory=list,
        description="List of other available prices from different wholesalers",
    )
    price_range: Optional[Dict[str, Decimal]] = Field(
        None, description="The minimum and maximum available prices (min, max)"
    )


class RegionOut(Schema):
    id: int
    name: str
    type: str
    code: Optional[str] = None
    parent_id: Optional[int] = None


class RegionWithHierarchyOut(Schema):
    id: int
    name: str
    type: str
    parent_id: Optional[int] = None
    code: Optional[str] = None
    hierarchical_name: str


T = TypeVar("T")


class PaginatedResponse(Schema, Generic[T]):
    items: List[T]
    total: int
    page: int
    size: int
    pages: int


class SearchResponse(Schema, Generic[T]):
    items: List[T]


class ExistenceResponse(Schema):
    product_exists: bool
    company_id: Optional[int] = None
    category_id: Optional[int] = None
