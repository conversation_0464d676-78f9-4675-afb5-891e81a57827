from django.contrib import admin
from .models import Company, Category, Region, Product


class CompanyAdmin(admin.ModelAdmin):
    list_display = ("name", "title", "slug", "created_at", "updated_at")
    search_fields = ("name", "title", "slug")
    prepopulated_fields = {"slug": ("name",)}
    list_filter = ("created_at", "updated_at")
    readonly_fields = ("created_at", "updated_at")


class CategoryAdmin(admin.ModelAdmin):
    list_display = ("name", "title", "slug", "created_at", "updated_at")
    search_fields = ("name", "title", "slug")
    prepopulated_fields = {"slug": ("name",)}
    list_filter = ("created_at", "updated_at")
    readonly_fields = ("created_at", "updated_at")


class RegionAdmin(admin.ModelAdmin):
    list_display = ("name", "type", "code", "parent", "is_active", "created_at")
    list_filter = ("type", "is_active", "created_at")
    search_fields = ("name", "code", "slug", "parent__name")
    prepopulated_fields = {"slug": ("name",)}
    readonly_fields = ("created_at", "updated_at")
    autocomplete_fields = ["parent"]
    fieldsets = (
        (None, {"fields": ("name", "type", "code", "slug", "is_active")}),
        ("Hierarchy", {"fields": ("parent",), "classes": ("collapse",)}),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


class ProductAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "barcode",
        "company",
        "category",
        "unit",
        "unit_count",
        "created_at",
    )
    list_filter = ("company", "category", "unit", "created_at")
    search_fields = ("name", "title", "barcode", "slug", "description")
    prepopulated_fields = {"slug": ("name",)}
    autocomplete_fields = ["company", "category"]
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (None, {"fields": ("name", "title", "barcode", "slug", "description")}),
        (
            "Categorization",
            {
                "fields": ("company", "category"),
            },
        ),
        (
            "Unit Information",
            {
                "fields": ("unit", "unit_count"),
            },
        ),
        (
            "Media",
            {
                "fields": ("image",),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
        (
            "Search Vector",
            {
                "fields": ("search_vector",),
                "classes": ("collapse",),
            },
        ),
    )


# Register all models with the admin site
admin.site.register(Company, CompanyAdmin)
admin.site.register(Category, CategoryAdmin)
admin.site.register(Region, RegionAdmin)
admin.site.register(Product, ProductAdmin)
