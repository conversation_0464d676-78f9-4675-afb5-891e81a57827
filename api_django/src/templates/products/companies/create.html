{% extends "base.html" %}

{% block title %}TagerPlus - Create Company{% endblock %}

{% block content %}
<div class="mb-4">
    <h1>Create New Company</h1>
</div>

<div class="card">
    <div class="card-body">
        <form method="post">
            {% csrf_token %}

            <div class="mb-3">
                <label for="{{ form.name.id_for_label }}" class="form-label">Company Name</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">{{ form.name.errors }}</div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.title.id_for_label }}" class="form-label">Company Title</label>
                {{ form.title }}
                {% if form.title.errors %}
                <div class="invalid-feedback d-block">{{ form.title.errors }}</div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.slug.id_for_label }}" class="form-label">Slug</label>
                {{ form.slug }}
                {% if form.slug.errors %}
                <div class="invalid-feedback d-block">{{ form.slug.errors }}</div>
                {% endif %}
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Create Company</button>
                <a href="{% url 'company_list' %}" class="btn btn-outline-secondary ms-2">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}