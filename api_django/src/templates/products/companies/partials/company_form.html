<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Add New Company</h5>
    </div>
    <div class="card-body">
        <form hx-post="{% url 'company_create' %}" hx-target="#companyList" hx-swap="outerHTML" hx-trigger="submit"
            class="needs-validation"
            hx-on::after-request="this.reset(); document.getElementById('companyFormContainer').innerHTML = '';">

            {% csrf_token %}

            <div class="mb-3">
                <label for="{{ form.name.id_for_label }}" class="form-label">Company Name</label>
                {{ form.name }}
                {% if form.name.errors %}
                <div class="invalid-feedback d-block">{{ form.name.errors }}</div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.title.id_for_label }}" class="form-label">Company Title</label>
                {{ form.title }}
                {% if form.title.errors %}
                <div class="invalid-feedback d-block">{{ form.title.errors }}</div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="{{ form.slug.id_for_label }}" class="form-label">Slug</label>
                {{ form.slug }}
                {% if form.slug.errors %}
                <div class="invalid-feedback d-block">{{ form.slug.errors }}</div>
                {% endif %}
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-primary">Save Company</button>
                <button type="button" class="btn btn-outline-secondary"
                    onclick="document.getElementById('companyFormContainer').innerHTML = ''">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>