{% extends "base.html" %}

{% block title %}TagerPlus - Categories{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Categories</h1>
    <button class="btn btn-primary" hx-get="{% url 'category_create' %}" hx-target="#categoryFormContainer"
        hx-swap="innerHTML">
        Add Category
    </button>
</div>

<!-- Category form container -->
<div id="categoryFormContainer" class="mb-4"></div>

<!-- Category list -->
<div id="categoryList" hx-get="{% url 'category_list' %}" hx-trigger="categoryListChanged from:body">
    {% include "products/categories/partials/category_list.html" %}
</div>
{% endblock %}