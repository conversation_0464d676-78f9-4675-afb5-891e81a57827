{% extends "base.html" %}

{% block title %}TagerPlus - Products{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Products</h1>
    <a href="{% url 'product_create' %}" class="btn btn-primary">Add Product</a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form hx-get="{% url 'product_list' %}" hx-target="#productList"
            hx-trigger="change from:#company_filter, change from:#category_filter" class="row g-3">

            <div class="col-md-5">
                <label for="company_filter" class="form-label">Filter by Company</label>
                <select id="company_filter" name="company_id" class="form-select">
                    <option value="">All Companies</option>
                    {% for company in companies %}
                    <option value="{{ company.id }}" {% if selected_company == company.id %} selected {% endif %} >
                        {{ company.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-5">
                <label for="category_filter" class="form-label">Filter by Category</label>
                <select id="category_filter" name="category_id" class="form-select">
                    <option value="">All Categories</option>
                    {% for category in categories %} 
                    <option value="{{ category.id }}" {% if selected_category == category.id %} selected {% endif %}>
                        {{ category.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-md-2 d-flex align-items-end">
                <a href="{% url 'product_list' %}" class="btn btn-outline-secondary w-100">Clear Filters</a>
            </div>
        </form>
    </div>
</div>

<!-- Product list -->
<div id="productList">
    {% include "products/products/partials/product_list.html" %}
</div>
{% endblock %}