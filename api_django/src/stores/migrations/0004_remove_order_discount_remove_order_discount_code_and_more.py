# Generated by Django 5.2.1 on 2025-07-04 00:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stores', '0003_order_deliver_at_order_status_order_status_reason_and_more'),
        ('wholesalers', '0008_alter_item_base_price'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='order',
            name='discount',
        ),
        migrations.RemoveField(
            model_name='order',
            name='discount_code',
        ),
        migrations.RemoveField(
            model_name='order',
            name='payment_method',
        ),
        migrations.RemoveField(
            model_name='order',
            name='payment_status',
        ),
        migrations.AddField(
            model_name='order',
            name='wholesaler',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='orders_wholesaler', to='wholesalers.wholesaler'),
            preserve_default=False,
        ),
    ]
