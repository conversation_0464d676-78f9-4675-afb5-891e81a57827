import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:grocery/views/entrypoint/wholesaler_entrypoint_ui.dart';

void main() {
  group('WholesalerEntryPointUI Tests', () {
    testWidgets('should display bottom navigation with 4 tabs',
        (WidgetTester tester) async {
      // Build the WholesalerEntryPointUI widget
      await tester.pumpWidget(
        const MaterialApp(
          home: WholesalerEntryPointUI(),
        ),
      );

      // Verify that the bottom navigation bar is present
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // Verify that all 4 navigation items are present in the bottom navigation
      final bottomNavBar = find.byType(BottomNavigationBar);
      expect(find.descendant(of: bottomNavBar, matching: find.text('الرئيسية')),
          findsOneWidget);
      expect(find.descendant(of: bottomNavBar, matching: find.text('الطلبات')),
          findsOneWidget);
      expect(find.descendant(of: bottomNavBar, matching: find.text('المنتجات')),
          findsOneWidget);
      expect(
          find.descendant(
              of: bottomNavBar, matching: find.text('الملف الشخصي')),
          findsOneWidget);
    });

    testWidgets('should navigate between tabs when tapped',
        (WidgetTester tester) async {
      // Build the WholesalerEntryPointUI widget
      await tester.pumpWidget(
        const MaterialApp(
          home: WholesalerEntryPointUI(),
        ),
      );

      // Initially should be on home tab (index 0)
      var bottomNavBar =
          tester.widget<BottomNavigationBar>(find.byType(BottomNavigationBar));
      expect(bottomNavBar.currentIndex, equals(0));

      // Find the bottom navigation bar and tap on Orders tab (index 1)
      final bottomNavBarFinder = find.byType(BottomNavigationBar);
      await tester.tap(find.descendant(
          of: bottomNavBarFinder, matching: find.text('الطلبات')));
      await tester.pump();

      // Verify navigation to Orders tab
      bottomNavBar =
          tester.widget<BottomNavigationBar>(find.byType(BottomNavigationBar));
      expect(bottomNavBar.currentIndex, equals(1));

      // Tap on Items tab (index 2)
      await tester.tap(find.descendant(
          of: bottomNavBarFinder, matching: find.text('المنتجات')));
      await tester.pump();

      // Verify navigation to Items tab
      bottomNavBar =
          tester.widget<BottomNavigationBar>(find.byType(BottomNavigationBar));
      expect(bottomNavBar.currentIndex, equals(2));

      // Tap on Profile tab (index 3)
      await tester.tap(find.descendant(
          of: bottomNavBarFinder, matching: find.text('الملف الشخصي')));
      await tester.pump();

      // Verify navigation to Profile tab
      bottomNavBar =
          tester.widget<BottomNavigationBar>(find.byType(BottomNavigationBar));
      expect(bottomNavBar.currentIndex, equals(3));
    });

    testWidgets('should display correct icons for each tab',
        (WidgetTester tester) async {
      // Build the WholesalerEntryPointUI widget
      await tester.pumpWidget(
        const MaterialApp(
          home: WholesalerEntryPointUI(),
        ),
      );

      // Verify that the correct icons are present in the bottom navigation
      final bottomNavBarFinder = find.byType(BottomNavigationBar);
      expect(
          find.descendant(
              of: bottomNavBarFinder, matching: find.byIcon(Icons.home)),
          findsOneWidget);
      expect(
          find.descendant(
              of: bottomNavBarFinder,
              matching: find.byIcon(Icons.receipt_long)),
          findsOneWidget);
      expect(
          find.descendant(
              of: bottomNavBarFinder, matching: find.byIcon(Icons.inventory_2)),
          findsOneWidget);
      expect(
          find.descendant(
              of: bottomNavBarFinder, matching: find.byIcon(Icons.person)),
          findsOneWidget);
    });
  });
}
