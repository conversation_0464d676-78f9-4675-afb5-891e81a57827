import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import '../lib/api/home/<USER>';
import '../lib/api/home/<USER>';
import '../lib/api/api.dart';

void main() {
  group('Search API Integration Tests', () {
    setUpAll(() {
      // Initialize HTTP service for testing
      // Note: In a real test environment, you might want to use a mock server
      // or test against a staging environment
    });

    test('ProductFilters should generate correct query parameters', () {
      // Test basic filters
      final filters = ProductFilters(
        search: 'test product',
        page: 2,
        pageSize: 10,
        categoryId: 5,
        companyId: 3,
      );

      final queryParams = filters.toQueryParams();

      expect(queryParams['search'], equals('test product'));
      expect(queryParams['page'], equals(2));
      expect(queryParams['page_size'], equals(10));
      expect(queryParams['category_id'], equals(5));
      expect(queryParams['company_id'], equals(3));
    });

    test('ProductFilters should handle empty search', () {
      final filters = ProductFilters(
        search: '',
        page: 1,
        pageSize: 20,
      );

      final queryParams = filters.toQueryParams();

      expect(queryParams.containsKey('search'), isFalse);
      expect(queryParams['page'], equals(1));
      expect(queryParams['page_size'], equals(20));
    });

    test('ProductFilters should handle null search', () {
      final filters = ProductFilters(
        search: null,
        page: 1,
        pageSize: 20,
      );

      final queryParams = filters.toQueryParams();

      expect(queryParams.containsKey('search'), isFalse);
      expect(queryParams['page'], equals(1));
      expect(queryParams['page_size'], equals(20));
    });

    test('ProductWithPricing should convert to ProductModel correctly', () {
      final product = ProductWithPricing(
        id: 1,
        name: 'Test Product',
        title: 'Test Product Title',
        barcode: '123456789',
        slug: 'test-product',
        description: 'Test description',
        imageUrl: 'https://example.com/image.jpg',
        unit: 'kg',
        unitCount: 2.5,
        basePrice: 10.99,
        otherPrice: 15.99,
      );

      final productModel = product.toProductModel();

      expect(productModel.name, equals('Test Product'));
      expect(productModel.weight, equals('2.5 kg'));
      expect(productModel.cover, equals('https://example.com/image.jpg'));
      expect(productModel.price, equals(10.99));
      expect(productModel.mainPrice, equals(15.99));
      expect(productModel.images, contains('https://example.com/image.jpg'));
    });

    test('ProductWithPricing should handle null image URL', () {
      final product = ProductWithPricing(
        id: 1,
        name: 'Test Product',
        title: 'Test Product Title',
        barcode: '123456789',
        slug: 'test-product',
        description: 'Test description',
        imageUrl: null,
        unit: 'piece',
        unitCount: 1.0,
        basePrice: 5.0,
        otherPrice: null,
      );

      final productModel = product.toProductModel();

      expect(productModel.cover, equals(''));
      expect(productModel.images, isEmpty);
      expect(productModel.price, equals(5.0));
      expect(
          productModel.mainPrice, equals(5.0)); // Should fallback to basePrice
    });

    test('ProductWithPricing should handle null prices', () {
      final product = ProductWithPricing(
        id: 1,
        name: 'Test Product',
        title: 'Test Product Title',
        barcode: '123456789',
        slug: 'test-product',
        description: 'Test description',
        imageUrl: null,
        unit: 'piece',
        unitCount: 1.0,
        basePrice: null,
        otherPrice: null,
      );

      final productModel = product.toProductModel();

      expect(productModel.price, equals(0.0));
      expect(productModel.mainPrice, equals(0.0));
    });

    // Note: The following tests would require a real API connection
    // In a production environment, you would either:
    // 1. Use a test/staging API endpoint
    // 2. Mock the HTTP service
    // 3. Use integration tests with a real backend

    /*
    test('HomeApiService.searchProducts should return valid results', () async {
      try {
        final results = await HomeApiService.searchProducts('test');
        
        expect(results, isA<List<ProductWithPricing>>());
        // Add more specific assertions based on your test data
        
      } catch (e) {
        // Handle expected errors (like network issues in test environment)
        print('API test skipped due to: $e');
      }
    });
    */
  });

  group('Error Handling Tests', () {
    test('HomeApiException should format messages correctly', () {
      final exception = HomeApiException('Test error message');

      expect(
          exception.toString(), equals('HomeApiException: Test error message'));
      expect(exception.message, equals('Test error message'));
    });
  });

  group('Search Flow Integration Tests', () {
    test('Search flow should handle empty query correctly', () {
      // Test that empty queries are handled properly
      final filters = ProductFilters(search: '');
      final queryParams = filters.toQueryParams();

      // Empty search should not be included in query params
      expect(queryParams.containsKey('search'), isFalse);
    });

    test('Search flow should handle whitespace-only query correctly', () {
      // Test that whitespace-only queries are handled properly
      final filters = ProductFilters(search: '   ');
      final queryParams = filters.toQueryParams();

      // Whitespace-only search should be included (will be trimmed by API)
      expect(queryParams['search'], equals('   '));
    });

    test('ProductWithPricing conversion should handle Arabic product names',
        () {
      final product = ProductWithPricing(
        id: 1,
        name: 'طماطم طازجة',
        title: 'طماطم حمراء طازجة',
        barcode: '123456789',
        slug: 'fresh-tomatoes',
        description: 'طماطم طازجة عالية الجودة',
        imageUrl: 'https://example.com/tomato.jpg',
        unit: 'كيلو',
        unitCount: 1.0,
        basePrice: 15.50,
        otherPrice: 18.00,
      );

      final productModel = product.toProductModel();

      expect(productModel.name, equals('طماطم طازجة'));
      expect(productModel.weight,
          equals('كيلو')); // Should not show "1.0" for whole numbers
      expect(productModel.price, equals(15.50));
      expect(productModel.mainPrice, equals(18.00));
    });

    test('ProductWithPricing conversion should handle fractional unit counts',
        () {
      final product = ProductWithPricing(
        id: 2,
        name: 'جبن أبيض',
        title: 'جبن أبيض طري',
        barcode: '987654321',
        slug: 'white-cheese',
        description: 'جبن أبيض طري طازج',
        imageUrl: null,
        unit: 'كيلو',
        unitCount: 0.5,
        basePrice: 25.75,
        otherPrice: null,
      );

      final productModel = product.toProductModel();

      expect(productModel.name, equals('جبن أبيض'));
      expect(productModel.weight, equals('0.5 كيلو'));
      expect(productModel.cover, equals(''));
      expect(productModel.images, isEmpty);
      expect(productModel.price, equals(25.75));
      expect(productModel.mainPrice,
          equals(25.75)); // Should fallback to basePrice
    });

    test('ProductWithPricing conversion should prefer name over title', () {
      final product = ProductWithPricing(
        id: 3,
        name: 'اسم المنتج',
        title: 'عنوان المنتج',
        barcode: '111222333',
        slug: 'product-slug',
        description: 'وصف المنتج',
        imageUrl: null,
        unit: 'قطعة',
        unitCount: 1.0,
        basePrice: 10.0,
        otherPrice: null,
      );

      final productModel = product.toProductModel();

      expect(productModel.name, equals('اسم المنتج'));
    });

    test(
        'ProductWithPricing conversion should fallback to title when name is empty',
        () {
      final product = ProductWithPricing(
        id: 4,
        name: '',
        title: 'عنوان المنتج',
        barcode: '444555666',
        slug: 'product-slug',
        description: 'وصف المنتج',
        imageUrl: null,
        unit: 'قطعة',
        unitCount: 1.0,
        basePrice: 10.0,
        otherPrice: null,
      );

      final productModel = product.toProductModel();

      expect(productModel.name, equals('عنوان المنتج'));
    });
  });
}
