import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:grocery/views/wholesaler/profile/wholesaler_profile_page.dart';

void main() {
  group('WholesalerProfilePage Tests', () {
    testWidgets('should display loading indicator initially', (WidgetTester tester) async {
      // Build the WholesalerProfilePage widget
      await tester.pumpWidget(
        const MaterialApp(
          home: WholesalerProfilePage(),
        ),
      );

      // Verify that the loading indicator is shown initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display app bar with correct title', (WidgetTester tester) async {
      // Build the WholesalerProfilePage widget
      await tester.pumpWidget(
        const MaterialApp(
          home: WholesalerProfilePage(),
        ),
      );

      // Verify that the app bar is present with correct title
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('الملف الشخصي'), findsOneWidget);
    });

    testWidgets('should have refresh indicator', (WidgetTester tester) async {
      // Build the WholesalerProfilePage widget
      await tester.pumpWidget(
        const MaterialApp(
          home: WholesalerProfilePage(),
        ),
      );

      // Verify that the refresh indicator is present
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });
  });
}
